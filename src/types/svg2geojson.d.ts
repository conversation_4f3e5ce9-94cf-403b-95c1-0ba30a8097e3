declare module 'svg2geo<PERSON><PERSON>' {
  interface Layer {
    name: string;
    geo: any;
  }

  interface Options {
    layers?: boolean;
    tolerance?: number;
  }

  export function geoFromSVGXML(
    svgString: string,
    callback: (layer: Layer) => void,
    options?: Options
  ): void;

  export function geoFromSVGFile(
    filename: string,
    callback: (layers: Layer[]) => void,
    options?: Options
  ): void;
}
