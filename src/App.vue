<script>
import mapboxgl from 'mapbox-gl';
import { cleanGeoJsonCoordinates } from './utils/svgToGeoJson.js';
import svg2geojson from 'svg2geojson';

export default {
  name: 'App',
  data() {
    return {
      map: null,
      svgData: null,
      svgLayer: null,
      geoJsonLayer: null,
      geoJsonData: null,
      mapCenter: { lng: 116.4074, lat: 39.9042 }, // 北京坐标
      mapZoom: 10,
      mapLoaded: false,
      customMapUrl: '',
      searchQuery: '',
      searchResults: [],
      isSearching: false,
      searchTimeout: null,
      savedCenters: [],
      geoJsonStrokeColor: '#ff0000', // GeoJSON线条颜色
      geoJsonFillColor: '#0000ff',   // GeoJSON填充颜色
      geoJsonStrokeWidth: 2,         // GeoJSON线条宽度
      statusMessage: '',
      statusType: 'info',
      isDragging: false,             // 是否正在拖拽
      dragStartPoint: null,          // 拖拽开始点
      geoJsonOffset: { lng: 0, lat: 0 }, // GeoJSON偏移量
      hasInitialGeoJSON: false,      // 是否已经生成过初始GeoJSON
      geoJsonCenter: null,           // 记录的GeoJSON中心点
      customMapUrlInput: '',         // 卫星地图源输入框内容

      isConverting: false,           // 是否正在转换中

      svgFile: null,
      svgContent: '',
      updatedSvgContent: '', // 包含MetaInfo的更新后SVG内容
      svgDomElement: null, // SVG DOM元素
      isDraggingSvg: false,
      isResizingSvg: false,
      dragStartPosition: null,
      showingSvg: false, // 是否正在显示SVG
      convertButtonText: '转换为 GeoJSON',
      
      // SVG调整变量
      svgSize: 200,                  // SVG大小(像素)
      svgRotation: 0,                // SVG旋转角度
      svgAspectRatio: 1,             // SVG高宽比
      // 新增缺失的变量
      geoJsonSize: 1.0,              // GeoJSON大小
      geoJsonRotation: 0,            // GeoJSON旋转角度
      useSatelliteMap: false,        // 是否使用卫星地图
      showGeoJSON: true,             // 是否显示GeoJSON
      showSVG: true,                 // 是否显示SVG
    }
  },
  mounted() {
    this.parseUrlParams();
    this.initMap();
  },
  methods: {
    // 解析URL参数
    parseUrlParams() {
      const urlParams = new URLSearchParams(window.location.search);
      
      // 解析mapUrl参数
      const mapUrl = urlParams.get('mapUrl');
      if (mapUrl) {
        this.customMapUrl = decodeURIComponent(mapUrl);
        this.customMapUrlInput = this.customMapUrl; // 同时设置到输入框
        this.useSatelliteMap = true; // 使用卫星地图时标记为卫星图模式
        this.showStatus(`使用卫星地图源: ${this.customMapUrl}`, 'info');
      }
      
      // 解析center参数
      const center = urlParams.get('center');
      if (center) {
        const [lng, lat] = center.split(',').map(Number);
        if (!isNaN(lng) && !isNaN(lat)) {
          this.mapCenter.lng = lng;
          this.mapCenter.lat = lat;
          this.showStatus(`设置中心点: ${lng}, ${lat}`, 'info');
        }
      }
      
      // 解析zoom参数
      // const zoom = urlParams.get('zoom');
      // if (zoom) {
      //   const zoomLevel = Number(zoom);
      //   if (!isNaN(zoomLevel)) {
      //     this.mapZoom = zoomLevel;
      //     this.showStatus(`设置缩放级别: ${zoomLevel}`, 'info');
      //   }
      // }
    },

    initMap() {
      // 设置 Mapbox GL Access Token (需要用户自己设置)
      mapboxgl.accessToken = 'pk.eyJ1IjoiZnVoYW8iLCJhIjoiY2poc3FsNTBsMDVxMzNxcnpzcThwMzkwbiJ9.-PDL_rfv3RFPgs0jTRWbEg'; // 用户需要替换为实际的Access Token
      
      // 选择地图样式
      let mapStyle;
      if (this.customMapUrl) {
        console.log("style map url:",this.customMapUrl)
        // 使用卫星地图源
        mapStyle = {
          "version": 8,
          "sources": {
            "custom-raster": {
              "type": "raster",
              "tiles": [this.customMapUrl],
              "tileSize": 256
            }
          },
          "layers": [
            {
              "id": "custom-layer",
              "type": "raster",
              "source": "custom-raster"
            }
          ]
        };
      } else {
        // 使用默认的Mapbox样式
        mapStyle = this.useSatelliteMap ? 'mapbox://styles/mapbox/satellite-v9' : 'mapbox://styles/mapbox/streets-v12';
      }
      
      this.map = new mapboxgl.Map({
        container: this.$refs.mapContainer,
        style: mapStyle,
        center: [this.mapCenter.lng, this.mapCenter.lat],
        zoom: this.mapZoom
      });

      this.map.on('load', () => {
        this.showStatus('地图加载完成', 'success');
        this.loadSavedCenters();
      });

      // 监听地图中心变化
      this.map.on('moveend', () => {
        const center = this.map.getCenter();
        this.mapCenter.lng = Number(center.lng.toFixed(6));
        this.mapCenter.lat = Number(center.lat.toFixed(6));
        this.mapZoom = Number(this.map.getZoom().toFixed(2)); // 保留两位小数
      });

      // 监听地图点击事件
      this.map.on('click', (e) => {
        if (!this.isDragging) {
        const { lng, lat } = e.lngLat;
        this.mapCenter.lng = Number(lng.toFixed(6));
        this.mapCenter.lat = Number(lat.toFixed(6));
        this.showStatus(`已设置中心点: ${lng.toFixed(6)}, ${lat.toFixed(6)}`, 'success');
        }
      });

      // 添加GeoJSON拖拽功能
      this.setupGeoJSONDragging();
    },

    async handleSvgUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      if (!file.type.includes('svg')) {
        this.showStatus('请选择有效的SVG文件', 'error');
        return;
      }

      try {
        this.svgFile = file;
        const originalSvgContent = await this.readFileAsText(file);

        // 检查SVG是否已有MetaInfo，如果没有则添加占位符
        this.svgContent = this.ensureMetaInfo(originalSvgContent);
        this.showStatus(`SVG文件 "${file.name}" 上传成功`, 'success');
        
        // 清除之前的GeoJSON
        this.clearGeoJsonLayer();
        
        // 在地图上显示SVG图片
        await this.displaySvgOnMap();
        
      } catch (error) {
        this.showStatus(`上传失败: ${error.message}`, 'error');
      }
    },

    /**
     * 确保SVG包含MetaInfo节点
     */
    ensureMetaInfo(svgContent) {
      const parser = new DOMParser();
      const svgDoc = parser.parseFromString(svgContent, "image/svg+xml");
      const svgElement = svgDoc.querySelector("svg");

      if (!svgElement) {
        throw new Error("无效的SVG格式");
      }

      // 检查是否已有MetaInfo
      const existingMetaInfo = svgElement.querySelector("MetaInfo");
      if (existingMetaInfo) {
        console.log("SVG已包含MetaInfo，无需添加");
        return svgContent;
      }

      // 创建MetaInfo占位符节点
      const metaInfo = svgDoc.createElementNS("http://www.prognoz.ru", "MetaInfo");
      metaInfo.setAttribute("xmlns", "http://www.prognoz.ru");

      const geo = svgDoc.createElement("Geo");

      // 添加两个占位符GeoItem
      const geoItem1 = svgDoc.createElement("GeoItem");
      geoItem1.setAttribute("X", "0");
      geoItem1.setAttribute("Y", "0");
      geoItem1.setAttribute("Latitude", "0");
      geoItem1.setAttribute("Longitude", "0");

      const geoItem2 = svgDoc.createElement("GeoItem");
      geoItem2.setAttribute("X", "100");
      geoItem2.setAttribute("Y", "100");
      geoItem2.setAttribute("Latitude", "0");
      geoItem2.setAttribute("Longitude", "0");

      geo.appendChild(geoItem1);
      geo.appendChild(geoItem2);
      metaInfo.appendChild(geo);

      // 将MetaInfo插入到SVG的开头
      svgElement.insertBefore(metaInfo, svgElement.firstChild);

      console.log("已为SVG添加MetaInfo占位符");
      return new XMLSerializer().serializeToString(svgDoc);
    },

    /**
     * 更新SVG中的MetaInfo地理坐标信息
     */
    updateMetaInfo(svgContent, geoBounds, svgBounds) {
      const parser = new DOMParser();
      const svgDoc = parser.parseFromString(svgContent, "image/svg+xml");
      const svgElement = svgDoc.querySelector("svg");

      if (!svgElement) {
        throw new Error("无效的SVG格式");
      }

      let metaInfo = svgElement.querySelector("MetaInfo");
      if (!metaInfo) {
        // 如果没有MetaInfo，创建一个
        metaInfo = svgDoc.createElementNS("http://www.prognoz.ru", "MetaInfo");
        metaInfo.setAttribute("xmlns", "http://www.prognoz.ru");
        svgElement.insertBefore(metaInfo, svgElement.firstChild);
      }

      let geo = metaInfo.querySelector("Geo");
      if (!geo) {
        geo = svgDoc.createElement("Geo");
        metaInfo.appendChild(geo);
      }

      // 清除现有的GeoItem
      const existingGeoItems = geo.querySelectorAll("GeoItem");
      existingGeoItems.forEach(item => item.remove());

      // 创建新的GeoItem
      const geoItem1 = svgDoc.createElement("GeoItem");
      geoItem1.setAttribute("X", svgBounds.minX.toString());
      geoItem1.setAttribute("Y", svgBounds.minY.toString());
      geoItem1.setAttribute("Latitude", geoBounds.topLeft.lat.toString());
      geoItem1.setAttribute("Longitude", geoBounds.topLeft.lng.toString());

      const geoItem2 = svgDoc.createElement("GeoItem");
      geoItem2.setAttribute("X", svgBounds.maxX.toString());
      geoItem2.setAttribute("Y", svgBounds.maxY.toString());
      geoItem2.setAttribute("Latitude", geoBounds.bottomRight.lat.toString());
      geoItem2.setAttribute("Longitude", geoBounds.bottomRight.lng.toString());

      geo.appendChild(geoItem1);
      geo.appendChild(geoItem2);

      console.log("已更新SVG MetaInfo:", {
        svg: svgBounds,
        geo: geoBounds
      });

      return new XMLSerializer().serializeToString(svgDoc);
    },

    async displaySvgOnMap() {
      if (!this.svgContent || !this.map) return;

      try {
        // 清除之前的SVG DOM元素
        this.clearSvgDomElement();

        // 计算SVG中g标签的实际边界和高宽比
        const gBounds = this.calculateGBoundsAndAspectRatio();
        
        // 创建SVG DOM元素
        const svgElement = this.createSvgDomElement();
        
        // 计算SVG在地图上的初始位置和大小
        const center = this.map.getCenter();
        const centerPixel = this.map.project(center);
        
        // 根据g标签边界设置初始大小，保持高宽比
        const baseSize = 300; // 基础大小
        let width, height;
        
        if (gBounds) {
          // 使用g标签的高宽比
          if (gBounds.aspectRatio >= 1) {
            // 宽度大于等于高度
            width = baseSize;
            height = baseSize / gBounds.aspectRatio;
          } else {
            // 高度大于宽度
            height = baseSize;
            width = baseSize * gBounds.aspectRatio;
          }
          // 保存高宽比到组件数据中
          this.svgAspectRatio = gBounds.aspectRatio;
        } else {
          // 如果无法计算边界，使用正方形
          width = height = baseSize;
          this.svgAspectRatio = 1;
        }
        
        this.svgSize = Math.max(width, height); // 保存最大边作为大小参考
        this.svgRotation = 0; // 重置旋转角度
        
        svgElement.style.left = `${centerPixel.x - width / 2}px`;
        svgElement.style.top = `${centerPixel.y - height / 2}px`;
        svgElement.style.width = `${width}px`;
        svgElement.style.height = `${height}px`;

        // 将SVG元素添加到地图容器
        const mapContainer = this.map.getContainer();
        mapContainer.appendChild(svgElement);

        // 保存SVG元素引用
        this.svgDomElement = svgElement;
        this.showingSvg = true;
        this.showSVG = true; // 显示SVG
        this.convertButtonText = '转换为 GeoJSON';

        // 添加SVG DOM元素的交互功能
        this.addSvgDomInteractions();

        const aspectRatioText = gBounds ? `${gBounds.aspectRatio.toFixed(2)}` : '1:1';
        this.showStatus(`SVG已显示在地图上，高宽比: ${aspectRatioText}`, 'success');

      } catch (error) {
        this.showStatus(`显示SVG失败: ${error.message}`, 'error');
      }
    },

    /**
     * 计算SVG中g标签的边界和高宽比
     */
    calculateGBoundsAndAspectRatio() {
      if (!this.svgContent) return null;

      try {
        // 解析SVG获取viewBox和内容边界
        const parser = new DOMParser();
        const svgDoc = parser.parseFromString(this.svgContent, "image/svg+xml");
        const svgElement = svgDoc.querySelector("svg");
        
        if (!svgElement) return null;

        // 获取SVG viewBox
        const viewBox = svgElement.getAttribute("viewBox");
        let svgWidth, svgHeight, svgMinX = 0, svgMinY = 0;

        if (viewBox) {
          const [minX, minY, width, height] = viewBox.split(/\s+/).map(Number);
          svgMinX = minX;
          svgMinY = minY;
          svgWidth = width;
          svgHeight = height;
        } else {
          svgWidth = parseFloat(svgElement.getAttribute("width")) || 100;
          svgHeight = parseFloat(svgElement.getAttribute("height")) || 100;
        }

        // 查找g标签内容的实际边界
        let targetContainer = svgElement;
        const gElements = svgElement.querySelectorAll("g");
        
        // 如果有g标签，优先使用第一个包含图形元素的g标签
        if (gElements.length > 0) {
          for (const g of gElements) {
            const graphicElements = g.querySelectorAll("path, polygon, polyline, rect, circle, ellipse, line");
            if (graphicElements.length > 0) {
              targetContainer = g;
              console.log(`使用g标签计算高宽比: id="${g.getAttribute('id') || '未命名'}", 包含${graphicElements.length}个图形元素`);
              break;
            }
          }
        }
        
        const elements = targetContainer.querySelectorAll("path, polygon, polyline, rect, circle, ellipse, line");
        let contentMinX = Infinity, contentMinY = Infinity;
        let contentMaxX = -Infinity, contentMaxY = -Infinity;

        elements.forEach((element) => {
          const bbox = this.getElementBounds(element);
          if (bbox) {
            contentMinX = Math.min(contentMinX, bbox.minX);
            contentMinY = Math.min(contentMinY, bbox.minY);
            contentMaxX = Math.max(contentMaxX, bbox.maxX);
            contentMaxY = Math.max(contentMaxY, bbox.maxY);
          }
        });

        // 如果没有找到内容边界，使用整个SVG区域
        if (!isFinite(contentMinX)) {
          contentMinX = svgMinX;
          contentMinY = svgMinY;
          contentMaxX = svgMinX + svgWidth;
          contentMaxY = svgMinY + svgHeight;
        }

        // 计算g标签内容的实际尺寸
        const contentWidth = contentMaxX - contentMinX;
        const contentHeight = contentMaxY - contentMinY;
        const aspectRatio = contentWidth / contentHeight;

        console.log('g标签边界计算结果:', {
          contentBounds: { contentMinX, contentMinY, contentMaxX, contentMaxY },
          contentSize: { width: contentWidth, height: contentHeight },
          aspectRatio: aspectRatio
        });

        return {
          minX: contentMinX,
          minY: contentMinY,
          maxX: contentMaxX,
          maxY: contentMaxY,
          width: contentWidth,
          height: contentHeight,
          aspectRatio: aspectRatio
        };

      } catch (error) {
        console.warn('计算g标签边界失败:', error);
        return null;
      }
    },

    /**
     * 创建SVG DOM元素
     */
    createSvgDomElement() {
      const container = document.createElement('div');
      container.className = 'svg-overlay';
      container.style.position = 'absolute';
      container.style.cursor = 'move';
      container.style.zIndex = '1000';
      container.style.pointerEvents = 'auto';
      container.style.border = '2px dashed #ff0000';
      container.style.background = 'rgba(255, 255, 255, 0.1)';
      
      // 创建SVG元素
      const svgWrapper = document.createElement('div');
      svgWrapper.style.width = '100%';
      svgWrapper.style.height = '100%';
      svgWrapper.style.overflow = 'hidden';
      svgWrapper.innerHTML = this.svgContent;
      
      // 确保SVG填满容器
      const svgElement = svgWrapper.querySelector('svg');
      if (svgElement) {
        svgElement.style.width = '100%';
        svgElement.style.height = '100%';
        svgElement.style.display = 'block';
      }
      
      container.appendChild(svgWrapper);
      
      // 添加调整大小的控制点
      const resizeHandle = document.createElement('div');
      resizeHandle.className = 'resize-handle';
      resizeHandle.style.position = 'absolute';
      resizeHandle.style.bottom = '0px';
      resizeHandle.style.right = '0px';
      resizeHandle.style.width = '10px';
      resizeHandle.style.height = '10px';
      resizeHandle.style.background = '#ff0000';
      resizeHandle.style.cursor = 'se-resize';
      resizeHandle.style.border = '1px solid #fff';
      
      container.appendChild(resizeHandle);
      
      return container;
    },

    /**
     * 清除SVG DOM元素
     */
    clearSvgDomElement() {
      if (this.svgDomElement) {
        // 清理事件监听器
        if (this.svgDomElement._mouseMoveHandler) {
          document.removeEventListener('mousemove', this.svgDomElement._mouseMoveHandler);
        }
        if (this.svgDomElement._mouseUpHandler) {
          document.removeEventListener('mouseup', this.svgDomElement._mouseUpHandler);
        }
        
        // 移除DOM元素
        this.svgDomElement.remove();
        this.svgDomElement = null;
        this.showSVG = false; // 更新SVG显示状态
      }
    },

    /**
     * 添加SVG DOM元素的交互功能
     */
    addSvgDomInteractions() {
      if (!this.svgDomElement) return;

      let isDragging = false;
      let isResizing = false;
      let startX, startY, startLeft, startTop, startWidth, startHeight;

      // 拖拽功能
      this.svgDomElement.addEventListener('mousedown', (e) => {
        if (e.target.className === 'resize-handle') {
          // 调整大小
          isResizing = true;
          startX = e.clientX;
          startY = e.clientY;
          startWidth = parseInt(this.svgDomElement.style.width);
          startHeight = parseInt(this.svgDomElement.style.height);
        } else {
          // 拖拽移动
          isDragging = true;
          startX = e.clientX;
          startY = e.clientY;
          startLeft = parseInt(this.svgDomElement.style.left);
          startTop = parseInt(this.svgDomElement.style.top);
        }
        
        e.preventDefault();
        e.stopPropagation();
      });

      // 全局鼠标移动事件
      const onMouseMove = (e) => {
        if (isDragging) {
          const deltaX = e.clientX - startX;
          const deltaY = e.clientY - startY;
          this.svgDomElement.style.left = `${startLeft + deltaX}px`;
          this.svgDomElement.style.top = `${startTop + deltaY}px`;
        } else if (isResizing) {
          const deltaX = e.clientX - startX;
          const deltaY = e.clientY - startY;
          
          // 保持高宽比的调整大小逻辑
          const maxDelta = Math.max(deltaX, deltaY);
          let newWidth, newHeight;
          
          if (this.svgAspectRatio >= 1) {
            // 宽度大于等于高度，以宽度为基准
            newWidth = Math.max(50, startWidth + maxDelta);
            newHeight = newWidth / this.svgAspectRatio;
          } else {
            // 高度大于宽度，以高度为基准
            newHeight = Math.max(50, startHeight + maxDelta);
            newWidth = newHeight * this.svgAspectRatio;
          }
          
          this.svgDomElement.style.width = `${newWidth}px`;
          this.svgDomElement.style.height = `${newHeight}px`;
          
          // 更新svgSize为最大边
          this.svgSize = Math.max(newWidth, newHeight);
        }
      };

      // 全局鼠标释放事件
      const onMouseUp = () => {
        isDragging = false;
        isResizing = false;
      };

      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);

      // 保存事件监听器引用，用于后续清理
      this.svgDomElement._mouseMoveHandler = onMouseMove;
      this.svgDomElement._mouseUpHandler = onMouseUp;
    },

    /**
     * 获取SVG DOM元素的地理坐标边界
     */
    getSvgGeoBounds() {
      if (!this.svgDomElement || !this.map || !this.svgContent) return null;

      // 解析SVG获取viewBox和内容边界
      const parser = new DOMParser();
      const svgDoc = parser.parseFromString(this.svgContent, "image/svg+xml");
      const svgElement = svgDoc.querySelector("svg");
      
      if (!svgElement) return null;

      // 获取SVG viewBox
      const viewBox = svgElement.getAttribute("viewBox");
      let originalSvgWidth, originalSvgHeight, svgMinX = 0, svgMinY = 0;

      if (viewBox) {
        const [minX, minY, width, height] = viewBox.split(/\s+/).map(Number);
        svgMinX = minX;
        svgMinY = minY;
        originalSvgWidth = width;
        originalSvgHeight = height;
      } else {
        originalSvgWidth = parseFloat(svgElement.getAttribute("width")) || 100;
        originalSvgHeight = parseFloat(svgElement.getAttribute("height")) || 100;
      }

      // 计算SVG内容的实际边界 - 优先查找g标签内的内容
      let targetContainer = svgElement;
      const gElements = svgElement.querySelectorAll("g");
      
      // 如果有g标签，优先使用第一个包含图形元素的g标签
      if (gElements.length > 0) {
        for (const g of gElements) {
          const graphicElements = g.querySelectorAll("path, polygon, polyline, rect, circle, ellipse, line");
          if (graphicElements.length > 0) {
            targetContainer = g;
            console.log(`使用g标签作为内容容器: id="${g.getAttribute('id') || '未命名'}", 包含${graphicElements.length}个图形元素`);
            break;
          }
        }
      }
      
      const elements = targetContainer.querySelectorAll("path, polygon, polyline, rect, circle, ellipse, line");
      let contentMinX = Infinity, contentMinY = Infinity;
      let contentMaxX = -Infinity, contentMaxY = -Infinity;

      console.log(`分析${elements.length}个图形元素的边界`);
      elements.forEach((element) => {
        const bbox = this.getElementBounds(element);
        if (bbox) {
          console.log(`元素${element.tagName}边界:`, bbox);
          contentMinX = Math.min(contentMinX, bbox.minX);
          contentMinY = Math.min(contentMinY, bbox.minY);
          contentMaxX = Math.max(contentMaxX, bbox.maxX);
          contentMaxY = Math.max(contentMaxY, bbox.maxY);
        }
      });

      // 如果没有找到内容边界，使用整个SVG区域
      if (!isFinite(contentMinX)) {
        contentMinX = svgMinX;
        contentMinY = svgMinY;
        contentMaxX = svgMinX + originalSvgWidth;
        contentMaxY = svgMinY + originalSvgHeight;
      }

      // 检测内容是否超出原始画布边界
      const contentExceedsCanvas = 
        contentMinX < svgMinX || 
        contentMinY < svgMinY || 
        contentMaxX > svgMinX + originalSvgWidth || 
        contentMaxY > svgMinY + originalSvgHeight;

      console.log('边界检测:', {
        原始画布: { svgMinX, svgMinY, width: originalSvgWidth, height: originalSvgHeight },
        内容边界: { contentMinX, contentMinY, contentMaxX, contentMaxY },
        内容超出画布: contentExceedsCanvas
      });

      // 如果内容超出，扩展画布以包含所有内容
      let effectiveSvgMinX = svgMinX;
      let effectiveSvgMinY = svgMinY;
      let effectiveSvgWidth = originalSvgWidth;
      let effectiveSvgHeight = originalSvgHeight;

      if (contentExceedsCanvas) {
        effectiveSvgMinX = Math.min(svgMinX, contentMinX);
        effectiveSvgMinY = Math.min(svgMinY, contentMinY);
        effectiveSvgWidth = Math.max(svgMinX + originalSvgWidth, contentMaxX) - effectiveSvgMinX;
        effectiveSvgHeight = Math.max(svgMinY + originalSvgHeight, contentMaxY) - effectiveSvgMinY;
        
        console.log('扩展画布:', {
          扩展前: { svgMinX, svgMinY, width: originalSvgWidth, height: originalSvgHeight },
          扩展后: { effectiveSvgMinX, effectiveSvgMinY, width: effectiveSvgWidth, height: effectiveSvgHeight }
        });
      }

      // 获取SVG DOM元素的屏幕边界
      const originalTransform = this.svgDomElement.style.transform;
      this.svgDomElement.style.transform = '';
      const rect = this.svgDomElement.getBoundingClientRect();
      const mapContainer = this.map.getContainer().getBoundingClientRect();
      this.svgDomElement.style.transform = originalTransform;

      // 计算SVG在地图容器中的位置
      const svgLeft = rect.left - mapContainer.left;
      const svgTop = rect.top - mapContainer.top;

      // 使用原始画布尺寸计算相对坐标（关键修复）
      const contentRelativeX = (contentMinX - svgMinX) / originalSvgWidth;
      const contentRelativeY = (contentMinY - svgMinY) / originalSvgHeight;
      const contentRelativeWidth = (contentMaxX - contentMinX) / originalSvgWidth;
      const contentRelativeHeight = (contentMaxY - contentMinY) / originalSvgHeight;

      // 计算内容在屏幕上的实际位置
      const contentScreenLeft = svgLeft + contentRelativeX * rect.width;
      const contentScreenTop = svgTop + contentRelativeY * rect.height;
      const contentScreenRight = contentScreenLeft + contentRelativeWidth * rect.width;
      const contentScreenBottom = contentScreenTop + contentRelativeHeight * rect.height;

      console.log('内容边界计算（使用原始画布尺寸）:', {
        原始viewBox: { svgMinX, svgMinY, width: originalSvgWidth, height: originalSvgHeight },
        content: { contentMinX, contentMinY, contentMaxX, contentMaxY },
        contentRelative: { contentRelativeX, contentRelativeY, contentRelativeWidth, contentRelativeHeight },
        svgDOM: { svgLeft, svgTop, width: rect.width, height: rect.height },
        contentScreen: { contentScreenLeft, contentScreenTop, contentScreenRight, contentScreenBottom }
      });

      // 转换为地理坐标 - 使用内容的屏幕位置
      const topLeft = this.map.unproject([contentScreenLeft, contentScreenTop]);
      const bottomRight = this.map.unproject([contentScreenRight, contentScreenBottom]);

      const bounds = {
        topLeft: { lng: topLeft.lng, lat: topLeft.lat },
        bottomRight: { lng: bottomRight.lng, lat: bottomRight.lat }
      };

      console.log('转换的地理边界（基于内容位置）:', bounds);
      console.log('地理边界范围:', {
        lngRange: bounds.bottomRight.lng - bounds.topLeft.lng,
        latRange: bounds.topLeft.lat - bounds.bottomRight.lat
      });

      return bounds;
    },

    getElementBounds(element) {
      const tagName = element.tagName.toLowerCase();

      try {
        switch (tagName) {
          case "rect":
            const x = parseFloat(element.getAttribute("x") || 0);
            const y = parseFloat(element.getAttribute("y") || 0);
            const width = parseFloat(element.getAttribute("width") || 0);
            const height = parseFloat(element.getAttribute("height") || 0);
            return {
              minX: x,
              minY: y,
              maxX: x + width,
              maxY: y + height,
            };

          case "circle":
            const cx = parseFloat(element.getAttribute("cx") || 0);
            const cy = parseFloat(element.getAttribute("cy") || 0);
            const r = parseFloat(element.getAttribute("r") || 0);
            return {
              minX: cx - r,
              minY: cy - r,
              maxX: cx + r,
              maxY: cy + r,
            };

          case "ellipse":
            const ecx = parseFloat(element.getAttribute("cx") || 0);
            const ecy = parseFloat(element.getAttribute("cy") || 0);
            const rx = parseFloat(element.getAttribute("rx") || 0);
            const ry = parseFloat(element.getAttribute("ry") || 0);
            return {
              minX: ecx - rx,
              minY: ecy - ry,
              maxX: ecx + rx,
              maxY: ecy + ry,
            };

          case "line":
            const x1 = parseFloat(element.getAttribute("x1") || 0);
            const y1 = parseFloat(element.getAttribute("y1") || 0);
            const x2 = parseFloat(element.getAttribute("x2") || 0);
            const y2 = parseFloat(element.getAttribute("y2") || 0);
            return {
              minX: Math.min(x1, x2),
              minY: Math.min(y1, y2),
              maxX: Math.max(x1, x2),
              maxY: Math.max(y1, y2),
            };

          case "polygon":
          case "polyline":
            const points = element.getAttribute("points");
            if (!points) return null;
            return this.getPointsBounds(points);

          case "path":
            const pathData = element.getAttribute("d");
            if (!pathData) return null;
            return this.getPathBounds(pathData);

          default:
            return null;
        }
      } catch (error) {
        console.warn(`计算元素边界失败:`, element, error);
        return null;
      }
    },

    getPointsBounds(pointsString) {
      const points = pointsString
        .trim()
        .split(/[\s,]+/)
        .filter((p) => p);
      if (points.length < 4) return null;

      let minX = Infinity, minY = Infinity;
      let maxX = -Infinity, maxY = -Infinity;

      for (let i = 0; i < points.length; i += 2) {
        const x = parseFloat(points[i]);
        const y = parseFloat(points[i + 1]);
        if (isFinite(x) && isFinite(y)) {
          minX = Math.min(minX, x);
          minY = Math.min(minY, y);
          maxX = Math.max(maxX, x);
          maxY = Math.max(maxY, y);
        }
      }

      return isFinite(minX) ? { minX, minY, maxX, maxY } : null;
    },

    getPathBounds(pathData) {
      try {
        // 使用更准确的路径解析
        let minX = Infinity, minY = Infinity;
        let maxX = -Infinity, maxY = -Infinity;
        let currentX = 0, currentY = 0;
        
        // 简化的路径命令解析
        const commands = pathData.match(/[MmLlHhVvCcSsQqTtAaZz][^MmLlHhVvCcSsQqTtAaZz]*/g);
        if (!commands) return null;
        
        for (const command of commands) {
          const cmd = command[0];
          const params = command.slice(1).match(/-?\d+\.?\d*/g) || [];
          const coords = params.map(Number);
          
          switch (cmd.toUpperCase()) {
            case 'M': // Move to
            case 'L': // Line to
              for (let i = 0; i < coords.length; i += 2) {
                const x = cmd === cmd.toUpperCase() ? coords[i] : currentX + coords[i];
                const y = cmd === cmd.toUpperCase() ? coords[i + 1] : currentY + coords[i + 1];
                currentX = x;
                currentY = y;
                minX = Math.min(minX, x);
                minY = Math.min(minY, y);
                maxX = Math.max(maxX, x);
                maxY = Math.max(maxY, y);
              }
              break;
              
            case 'H': // Horizontal line
              for (let i = 0; i < coords.length; i++) {
                const x = cmd === cmd.toUpperCase() ? coords[i] : currentX + coords[i];
                currentX = x;
                minX = Math.min(minX, x);
                maxX = Math.max(maxX, x);
              }
              break;
              
            case 'V': // Vertical line
              for (let i = 0; i < coords.length; i++) {
                const y = cmd === cmd.toUpperCase() ? coords[i] : currentY + coords[i];
                currentY = y;
                minY = Math.min(minY, y);
                maxY = Math.max(maxY, y);
              }
              break;
              
            case 'C': // Cubic Bezier curve
              for (let i = 0; i < coords.length; i += 6) {
                // 处理控制点和终点
                for (let j = 0; j < 6; j += 2) {
                  if (i + j + 1 < coords.length) {
                    const x = cmd === cmd.toUpperCase() ? coords[i + j] : currentX + coords[i + j];
                    const y = cmd === cmd.toUpperCase() ? coords[i + j + 1] : currentY + coords[i + j + 1];
                    minX = Math.min(minX, x);
                    minY = Math.min(minY, y);
                    maxX = Math.max(maxX, x);
                    maxY = Math.max(maxY, y);
                  }
                }
                if (i + 5 < coords.length) {
                  currentX = cmd === cmd.toUpperCase() ? coords[i + 4] : currentX + coords[i + 4];
                  currentY = cmd === cmd.toUpperCase() ? coords[i + 5] : currentY + coords[i + 5];
                }
              }
              break;
              
            case 'Q': // Quadratic Bezier curve
              for (let i = 0; i < coords.length; i += 4) {
                // 处理控制点和终点
                for (let j = 0; j < 4; j += 2) {
                  if (i + j + 1 < coords.length) {
                    const x = cmd === cmd.toUpperCase() ? coords[i + j] : currentX + coords[i + j];
                    const y = cmd === cmd.toUpperCase() ? coords[i + j + 1] : currentY + coords[i + j + 1];
                    minX = Math.min(minX, x);
                    minY = Math.min(minY, y);
                    maxX = Math.max(maxX, x);
                    maxY = Math.max(maxY, y);
                  }
                }
                if (i + 3 < coords.length) {
                  currentX = cmd === cmd.toUpperCase() ? coords[i + 2] : currentX + coords[i + 2];
                  currentY = cmd === cmd.toUpperCase() ? coords[i + 3] : currentY + coords[i + 3];
                }
              }
              break;
              
            case 'A': // Arc
              for (let i = 0; i < coords.length; i += 7) {
                if (i + 6 < coords.length) {
                  const x = cmd === cmd.toUpperCase() ? coords[i + 5] : currentX + coords[i + 5];
                  const y = cmd === cmd.toUpperCase() ? coords[i + 6] : currentY + coords[i + 6];
                  currentX = x;
                  currentY = y;
                  minX = Math.min(minX, x);
                  minY = Math.min(minY, y);
                  maxX = Math.max(maxX, x);
                  maxY = Math.max(maxY, y);
                }
              }
              break;
          }
        }
        
        return isFinite(minX) ? { minX, minY, maxX, maxY } : null;
      } catch (error) {
        console.warn('路径边界计算失败，使用简化方法:', error);
        // 回退到简化方法
        const coords = pathData.match(/-?\d+\.?\d*/g);
        if (!coords || coords.length < 4) return null;

        let minX = Infinity, minY = Infinity;
        let maxX = -Infinity, maxY = -Infinity;

        for (let i = 0; i < coords.length; i += 2) {
          const x = parseFloat(coords[i]);
          const y = parseFloat(coords[i + 1]);
          if (isFinite(x) && isFinite(y)) {
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
          }
        }

        return isFinite(minX) ? { minX, minY, maxX, maxY } : null;
      }
    },

    /**
     * 获取SVG内容的边界坐标
     */
    getSvgContentBounds() {
      if (!this.svgContent) return null;

      try {
        const parser = new DOMParser();
        const svgDoc = parser.parseFromString(this.svgContent, "image/svg+xml");
        const svgElement = svgDoc.querySelector("svg");

        if (!svgElement) return null;

        // 获取SVG尺寸
        const viewBox = svgElement.getAttribute("viewBox");
        let svgWidth, svgHeight, svgMinX = 0, svgMinY = 0;

        if (viewBox) {
          const [minX, minY, width, height] = viewBox.split(/\s+/).map(Number);
          svgMinX = minX;
          svgMinY = minY;
          svgWidth = width;
          svgHeight = height;
        } else {
          svgWidth = parseFloat(svgElement.getAttribute("width")) || 100;
          svgHeight = parseFloat(svgElement.getAttribute("height")) || 100;
        }

        // 计算内容边界（这里使用整个SVG区域作为内容边界）
        return {
          minX: svgMinX,
          minY: svgMinY,
          maxX: svgMinX + svgWidth,
          maxY: svgMinY + svgHeight
        };
      } catch (error) {
        console.error('获取SVG内容边界失败:', error);
        return null;
      }
    },

    /**
     * 使用svg2geojson库转换SVG
     */
    async convertWithSvg2GeoJson(svgContent) {
      try {
        // 使用svg2geojson库进行转换
        const geoJson = svg2geojson(svgContent);

        console.log('svg2geojson转换结果:', geoJson);

        // 如果转换结果是字符串，解析为对象
        if (typeof geoJson === 'string') {
          return JSON.parse(geoJson);
        }

        return geoJson;
      } catch (error) {
        console.error('svg2geojson转换失败:', error);
        throw new Error(`svg2geojson转换失败: ${error.message}`);
      }
    },

    async convertToGeoJSON() {
      if (!this.svgContent || !this.svgDomElement) {
        this.showStatus('请先上传SVG文件并在地图上调整位置', 'error');
        return;
      }

      this.isConverting = true;
      this.convertButtonText = '转换中...';

      try {
        // 使用DOM元素获取地理边界
        const geoBounds = this.getSvgGeoBounds();
        if (!geoBounds) {
          throw new Error('无法获取SVG的地理边界');
        }

        // 获取SVG内容边界
        const svgBounds = this.getSvgContentBounds();
        if (!svgBounds) {
          throw new Error('无法获取SVG内容边界');
        }

        // 更新SVG中的MetaInfo
        const updatedSvgContent = this.updateMetaInfo(this.svgContent, geoBounds, svgBounds);

        // 保存更新后的SVG内容，以便下载
        this.updatedSvgContent = updatedSvgContent;

        // 添加调试信息
        console.log('开始转换，SVG边界:', geoBounds);
        console.log('SVG内容边界:', svgBounds);
        console.log('SVG内容长度:', updatedSvgContent.length);
        console.log('SVG旋转角度:', this.svgRotation);

        // 使用svg2geojson库进行转换
        const geoJson = await this.convertWithSvg2GeoJson(updatedSvgContent);
        
        // 清理GeoJSON数据
        this.geoJsonData = cleanGeoJsonCoordinates(geoJson);
        
        console.log('转换后的GeoJSON:', this.geoJsonData);
        
        // 统计特征类型（排除测试边界框）
        const featureTypes = {};
        const actualFeatures = this.geoJsonData.features.filter(feature => 
          !feature.properties || feature.properties.type !== 'test'
        );
        
        actualFeatures.forEach(feature => {
          const type = feature.geometry.type;
          featureTypes[type] = (featureTypes[type] || 0) + 1;
        });
        
        console.log('转换的特征类型统计:', featureTypes);
        console.log('实际SVG特征数量:', actualFeatures.length);

        // 显示GeoJSON
        this.displayGeoJsonOnMap();

        const typesList = Object.entries(featureTypes).map(([type, count]) => `${count}个${type}`).join(', ');
        this.showStatus(`转换成功！生成了 ${actualFeatures.length} 个地理特征 (${typesList})`, 'success');
        this.convertButtonText = '重新调整';

      } catch (error) {
        this.showStatus(`转换失败: ${error.message}`, 'error');
        this.convertButtonText = '转换为 GeoJSON';
      } finally {
        this.isConverting = false;
      }
    },

    displayGeoJsonOnMap() {
      if (!this.geoJsonData || !this.map) return;

      // 清除之前的GeoJSON图层
      this.clearGeoJsonLayer();

      // 添加GeoJSON源
      this.map.addSource('geojson-data', {
        type: 'geojson',
        data: this.geoJsonData
      });

      // 添加填充图层（多边形和多多边形）
      this.map.addLayer({
        id: 'geojson-fill',
        type: 'fill',
        source: 'geojson-data',
        filter: ['any', 
          ['==', ['geometry-type'], 'Polygon'], 
          ['==', ['geometry-type'], 'MultiPolygon']
        ],
        paint: {
          'fill-color': this.geoJsonFillColor,
          'fill-opacity': 0.5
        }
      });

      // 添加边框图层（所有类型的线条和多边形边框）
      this.map.addLayer({
        id: 'geojson-stroke',
        type: 'line',
        source: 'geojson-data',
        filter: ['any', 
          ['==', ['geometry-type'], 'Polygon'], 
          ['==', ['geometry-type'], 'MultiPolygon'],
          ['==', ['geometry-type'], 'LineString'],
          ['==', ['geometry-type'], 'MultiLineString']
        ],
        paint: {
          'line-color': this.geoJsonStrokeColor,
          'line-width': this.geoJsonStrokeWidth
        }
      });



      // 添加点图层
      this.map.addLayer({
        id: 'geojson-points',
        type: 'circle',
        source: 'geojson-data',
        filter: ['==', ['geometry-type'], 'Point'],
        paint: {
          'circle-color': this.geoJsonStrokeColor,
          'circle-radius': Math.max(4, this.geoJsonStrokeWidth * 2)
        }
      });

      // 不自动缩放，保持当前视图来检查对齐
      // this.zoomToGeoJson();
    },

    resetToSvgMode() {
      if (!this.svgContent) {
        this.showStatus('请先上传SVG文件', 'error');
        return;
      }

      // 清除GeoJSON图层
      this.clearGeoJsonLayer();
      this.geoJsonData = null;

      // 重新显示SVG DOM元素
      this.displaySvgOnMap();
      this.convertButtonText = '转换为 GeoJSON';
    },

    clearGeoJsonLayer() {
      if (!this.map) return;

      const layerIds = ['geojson-fill', 'geojson-stroke', 'geojson-points'];
      
      layerIds.forEach(layerId => {
        if (this.map.getLayer(layerId)) {
          this.map.removeLayer(layerId);
        }
      });

      if (this.map.getSource('geojson-data')) {
        this.map.removeSource('geojson-data');
      }
    },

    zoomToGeoJson() {
      if (!this.geoJsonData || !this.map) return;

      try {
        // 计算GeoJSON的边界框
        let minLng = Infinity, minLat = Infinity;
        let maxLng = -Infinity, maxLat = -Infinity;

        this.geoJsonData.features.forEach(feature => {
          if (feature.geometry && feature.geometry.coordinates) {
            this.extractCoordinates(feature.geometry.coordinates).forEach(coord => {
              const [lng, lat] = coord;
              minLng = Math.min(minLng, lng);
              minLat = Math.min(minLat, lat);
              maxLng = Math.max(maxLng, lng);
              maxLat = Math.max(maxLat, lat);
            });
          }
        });

        if (isFinite(minLng) && isFinite(minLat) && isFinite(maxLng) && isFinite(maxLat)) {
          this.map.fitBounds(
            [[minLng, minLat], [maxLng, maxLat]],
            { padding: 50, duration: 1000 }
          );
        }
      } catch (error) {
        console.warn('缩放到GeoJSON范围失败:', error);
      }
    },

    extractCoordinates(coords) {
      if (typeof coords[0] === 'number') {
        return [coords];
      }
      
      let result = [];
      coords.forEach(coord => {
        result = result.concat(this.extractCoordinates(coord));
      });
      return result;
    },

    readFileAsText(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(e.target.result);
        reader.onerror = reject;
        reader.readAsText(file);
      });
    },

    /**
     * 清空所有数据
     */
    clearAll() {
      // 清除SVG相关
      this.clearSvgDomElement();
      this.svgFile = null;
      this.svgContent = '';
      
      // 清除GeoJSON相关
      this.clearGeoJsonLayer();
      this.geoJsonData = null;
      
      // 重置状态
      this.showingSvg = false;
      this.convertButtonText = '转换为 GeoJSON';
      this.isConverting = false;
      
      // 清空文件输入
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = '';
      }
      if (this.$refs.geoJsonFileInput) {
        this.$refs.geoJsonFileInput.value = '';
      }
      
      this.showStatus('所有数据已清空', 'info');
    },

    /**
     * 显示状态信息
     */
    showStatus(message, type = 'info') {
      this.statusMessage = message;
      this.statusType = type;
      
      // 自动清除状态信息
      setTimeout(() => {
        this.statusMessage = '';
      }, type === 'error' ? 5000 : 3000);
    },

    /**
     * 设置 GeoJSON 拖拽功能
     */
    setupGeoJSONDragging() {
      // 这个方法在新的架构中不需要，因为我们使用SVG图片拖拽
      // 但为了兼容性，保留一个空实现
      console.log('GeoJSON 拖拽功能已初始化');
    },

    /**
     * 处理GeoJSON文件上传
     */
    handleGeoJSONUpload(event) {
      console.log('GeoJSON文件上传功能待实现', event);
    },

    /**
     * 搜索地址
     */
    searchLocation() {
      console.log('地址搜索功能待实现');
    },

    /**
     * 搜索输入处理
     */
    onSearchInput() {
      console.log('搜索输入处理待实现');
    },

    /**
     * 选择搜索结果
     */
    selectLocation(result) {
      console.log('选择搜索结果功能待实现', result);
    },

    /**
     * 应用卫星地图URL
     */
    applyCustomMapUrl() {
      console.log('应用卫星地图URL功能待实现');
    },

    /**
     * 清除卫星地图URL
     */
    clearCustomMapUrl() {
      console.log('清除卫星地图URL功能待实现');
    },

    /**
     * 更新地图中心
     */
    updateMapCenter() {
      if (this.map) {
        this.map.setCenter([this.mapCenter.lng, this.mapCenter.lat]);
      }
    },

    /**
     * 更新地图缩放
     */
    updateMapZoom() {
      if (this.map) {
        this.map.setZoom(this.mapZoom);
      }
    },

    /**
     * 切换地图样式
     */
    toggleMapStyle() {
      if (!this.map) return;

      try {
        let newMapStyle;
        
                 if (this.customMapUrl) {
           // 如果有自定义地图URL，在卫星地图(自定义)和默认地图之间切换
           if (this.useSatelliteMap) {
             // 当前显示卫星地图(自定义)，切换到默认街道地图
             newMapStyle = 'mapbox://styles/mapbox/streets-v12';
             this.showStatus('已切换到街道地图', 'info');
           } else {
             // 当前显示默认地图，切换到卫星地图(自定义)
             newMapStyle = {
               "version": 8,
               "sources": {
                 "custom-raster": {
                   "type": "raster",
                   "tiles": [this.customMapUrl],
                   "tileSize": 256
                 }
               },
               "layers": [
                 {
                   "id": "custom-layer",
                   "type": "raster",
                   "source": "custom-raster"
                 }
               ]
             };
             this.showStatus('已切换到卫星地图', 'info');
           }
         } else {
           // 没有自定义地图URL，在卫星图和街道图之间切换
           if (this.useSatelliteMap) {
             newMapStyle = 'mapbox://styles/mapbox/satellite-v9';
             this.showStatus('已切换到卫星地图', 'info');
           } else {
             newMapStyle = 'mapbox://styles/mapbox/streets-v12';
             this.showStatus('已切换到街道地图', 'info');
           }
         }

                 // 保存当前的地图状态
         const currentCenter = this.map.getCenter();
         const currentZoom = this.map.getZoom();

        // 更新地图样式
        this.map.setStyle(newMapStyle);

        // 等待新样式加载完成后恢复图层
        this.map.once('styledata', () => {
          // 恢复地图视图
          this.map.setCenter(currentCenter);
          this.map.setZoom(currentZoom);

                     // 恢复SVG DOM元素
           if (this.svgContent && this.showingSvg) {
             this.displaySvgOnMap();
           }

           // 恢复GeoJSON图层
           if (this.geoJsonData) {
             this.displayGeoJsonOnMap();
           }
        });

      } catch (error) {
        this.showStatus(`切换地图样式失败: ${error.message}`, 'error');
        console.error('切换地图样式失败:', error);
      }
    },

    /**
     * 保存当前中心点
     */
    saveCurrentCenter() {
      if (!this.map) return;

      const center = this.map.getCenter();
      const zoom = this.map.getZoom();
      
      // 生成中心点名称
      const name = prompt('请输入中心点名称:', `位置 ${this.savedCenters.length + 1}`);
      if (!name) return;

      const centerData = {
        name: name.trim(),
        lng: Number(center.lng.toFixed(6)),
        lat: Number(center.lat.toFixed(6)),
        zoom: Number(zoom.toFixed(2)),
        date: new Date().toLocaleString('zh-CN')
      };

      this.savedCenters.push(centerData);
      this.saveCentersToStorage();
      this.showStatus(`中心点 "${name}" 已保存`, 'success');
    },

    /**
     * 跳转到中心点
     */
    jumpToCenter(center) {
      if (!this.map || !center) return;

      this.map.flyTo({
        center: [center.lng, center.lat],
        zoom: center.zoom,
        speed: 1.2,
        curve: 1.42
      });

      // 更新数据
      this.mapCenter.lng = center.lng;
      this.mapCenter.lat = center.lat;
      this.mapZoom = center.zoom;

      this.showStatus(`已跳转到 "${center.name}"`, 'success');
    },

    /**
     * 删除保存的中心点
     */
    deleteSavedCenter(index) {
      if (index >= 0 && index < this.savedCenters.length) {
        const centerName = this.savedCenters[index].name;
        
        if (confirm(`确定要删除中心点 "${centerName}" 吗？`)) {
          this.savedCenters.splice(index, 1);
          this.saveCentersToStorage();
          this.showStatus(`中心点 "${centerName}" 已删除`, 'info');
        }
      }
    },

    /**
     * 更新GeoJSON大小
     */
    updateGeoJSONSize() {
      console.log('更新GeoJSON大小功能待实现');
    },

    /**
     * 更新GeoJSON旋转
     */
    updateGeoJSONRotation() {
      console.log('更新GeoJSON旋转功能待实现');
    },



    /**
     * 更新SVG大小
     */
    updateSvgSize() {
      if (!this.svgDomElement) return;
      
      // 获取SVG当前的中心位置
      const currentLeft = parseInt(this.svgDomElement.style.left);
      const currentTop = parseInt(this.svgDomElement.style.top);
      const currentWidth = parseInt(this.svgDomElement.style.width);
      const currentHeight = parseInt(this.svgDomElement.style.height);
      
      // 计算中心点
      const centerX = currentLeft + currentWidth / 2;
      const centerY = currentTop + currentHeight / 2;
      
      // 根据高宽比计算新的宽度和高度
      let newWidth, newHeight;
      if (this.svgAspectRatio >= 1) {
        // 宽度大于等于高度，以svgSize为宽度
        newWidth = this.svgSize;
        newHeight = this.svgSize / this.svgAspectRatio;
      } else {
        // 高度大于宽度，以svgSize为高度
        newHeight = this.svgSize;
        newWidth = this.svgSize * this.svgAspectRatio;
      }
      
      // 应用新的大小，保持中心点不变
      this.svgDomElement.style.width = `${newWidth}px`;
      this.svgDomElement.style.height = `${newHeight}px`;
      this.svgDomElement.style.left = `${centerX - newWidth / 2}px`;
      this.svgDomElement.style.top = `${centerY - newHeight / 2}px`;
      
      this.showStatus(`SVG大小已调整为 ${Math.round(newWidth)}×${Math.round(newHeight)}px (高宽比: ${this.svgAspectRatio.toFixed(2)})`, 'info');
    },

    /**
     * 更新SVG旋转
     */
    updateSvgRotation() {
      if (!this.svgDomElement) return;
      
      // 应用旋转变换
      const transform = `rotate(${this.svgRotation}deg)`;
      this.svgDomElement.style.transform = transform;
      
      this.showStatus(`SVG旋转角度已调整为 ${this.svgRotation}°`, 'info');
    },

    /**
     * 清除GeoJSON
     */
    clearGeoJSON() {
      this.clearGeoJsonLayer();
      this.geoJsonData = null;
      this.showStatus('GeoJSON已清除', 'info');
    },

    /**
     * 更新GeoJSON颜色
     */
    updateGeoJSONColors() {
      if (!this.map) return;

      try {
        // 更新填充图层样式
        if (this.map.getLayer('geojson-fill')) {
          this.map.setPaintProperty('geojson-fill', 'fill-color', this.geoJsonFillColor);
          this.map.setPaintProperty('geojson-fill', 'fill-opacity', 0.5);
        }

        // 更新边框图层样式
        if (this.map.getLayer('geojson-stroke')) {
          this.map.setPaintProperty('geojson-stroke', 'line-color', this.geoJsonStrokeColor);
          this.map.setPaintProperty('geojson-stroke', 'line-width', this.geoJsonStrokeWidth);
        }

        // 更新点图层样式
        if (this.map.getLayer('geojson-points')) {
          this.map.setPaintProperty('geojson-points', 'circle-color', this.geoJsonStrokeColor);
          this.map.setPaintProperty('geojson-points', 'circle-radius', Math.max(4, this.geoJsonStrokeWidth * 2));
        }



        this.showStatus('GeoJSON样式已更新', 'info');
      } catch (error) {
        console.error('更新GeoJSON样式失败:', error);
        this.showStatus('更新样式失败: ' + error.message, 'error');
      }
    },

    /**
     * 切换GeoJSON显示
     */
    toggleGeoJSONDisplay() {
      if (!this.map) return;
      
      const layerIds = ['geojson-fill', 'geojson-stroke', 'geojson-points'];
      
      if (this.showGeoJSON) {
        // 显示GeoJSON图层
        layerIds.forEach(layerId => {
          if (this.map.getLayer(layerId)) {
            this.map.setLayoutProperty(layerId, 'visibility', 'visible');
          }
        });
        this.showStatus('GeoJSON已显示', 'info');
      } else {
        // 隐藏GeoJSON图层
        layerIds.forEach(layerId => {
          if (this.map.getLayer(layerId)) {
            this.map.setLayoutProperty(layerId, 'visibility', 'none');
          }
        });
        this.showStatus('GeoJSON已隐藏', 'info');
      }
    },

    /**
     * 切换SVG显示
     */
    toggleSVGDisplay() {
      if (!this.svgDomElement) return;
      
      if (this.showSVG) {
        // 显示SVG
        this.svgDomElement.style.display = 'block';
        this.showStatus('SVG已显示', 'info');
      } else {
        // 隐藏SVG
        this.svgDomElement.style.display = 'none';
        this.showStatus('SVG已隐藏', 'info');
      }
    },

    /**
     * 下载GeoJSON
     */
    downloadGeoJSON() {
      if (!this.geoJsonData) {
        this.showStatus('没有可下载的GeoJSON数据', 'error');
        return;
      }

      const dataStr = JSON.stringify(this.geoJsonData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = 'converted.geojson';
      link.click();
      
      URL.revokeObjectURL(url);
      this.showStatus('GeoJSON文件已下载', 'success');
    },

    /**
     * 下载包含MetaInfo的SVG文件
     */
    downloadUpdatedSVG() {
      if (!this.updatedSvgContent) {
        this.showStatus('没有可下载的更新后SVG数据', 'error');
        return;
      }

      const dataBlob = new Blob([this.updatedSvgContent], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement('a');
      link.href = url;
      link.download = 'updated_with_metainfo.svg';
      link.click();

      URL.revokeObjectURL(url);
      this.showStatus('包含MetaInfo的SVG文件已下载', 'success');
    },

    /**
     * 复制GeoJSON到剪贴板
     */
    async copyGeoJSONToClipboard() {
      if (!this.geoJsonData) {
        this.showStatus('没有可复制的GeoJSON数据', 'error');
        return;
      }

      try {
        const dataStr = JSON.stringify(this.geoJsonData, null, 2);
        await navigator.clipboard.writeText(dataStr);
        this.showStatus('GeoJSON已复制到剪贴板', 'success');
      } catch (error) {
        this.showStatus('复制失败: ' + error.message, 'error');
      }
    },

    /**
     * 提交GeoJSON
     */
    submitGeoJSON() {
      console.log('提交GeoJSON功能待实现');
    },

    /**
     * 适应地图视图到GeoJSON
     */
    fitMapToGeoJSON() {
      this.zoomToGeoJson();
    },

    /**
     * 加载保存的中心点
     */
    loadSavedCenters() {
      try {
        const saved = localStorage.getItem('mapCenters');
        if (saved) {
          this.savedCenters = JSON.parse(saved);
        }
      } catch (error) {
        console.log('加载保存的中心点失败:', error);
      }
    },

    /**
     * 保存中心点到本地存储
     */
    saveCentersToStorage() {
      try {
        localStorage.setItem('mapCenters', JSON.stringify(this.savedCenters));
      } catch (error) {
        console.error('保存中心点失败:', error);
        this.showStatus('保存中心点失败', 'error');
      }
    },

    // ... existing other methods ...
  }
}
</script>

<template>
  <div id="app">
    <!-- 地图容器 -->
    <div id="map" ref="mapContainer"></div>
    
    <!-- 右侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-content">
        <h2>SVG to GeoJSON</h2>
        
        <!-- 文件导入 -->
      <div class="section">
          <h3>文件导入</h3>
          
          <!-- SVG 文件上传 -->
          <div class="file-upload-group">
        <input 
          type="file" 
          accept=".svg" 
          @change="handleSvgUpload" 
              ref="fileInput"
          class="file-input"
        />
            <button @click="$refs.fileInput.click()" class="upload-btn">
              📁 选择 SVG 文件
          </button>
        </div>

          <!-- GeoJSON 文件上传 -->
          <div class="file-upload-group">
            <input 
              type="file" 
              accept=".json,.geojson" 
              @change="handleGeoJSONUpload" 
              ref="geoJsonFileInput"
              class="file-input"
            />
            <button @click="$refs.geoJsonFileInput.click()" class="upload-btn secondary">
              📄 导入 GeoJSON 文件
            </button>
          </div>

          <button @click="clearAll" class="clear-btn" v-if="svgContent || geoJsonData">
            🗑️ 清空所有数据
          </button>
        </div>

        <!-- 地址搜索 -->
        <div class="section">
          <h3>地址搜索</h3>
        <div class="form-group">
          <div class="search-container">
            <input 
              v-model="searchQuery" 
              type="text" 
                placeholder="搜索地址或地点..."
                @keyup.enter="searchLocation"
                @input="onSearchInput"
              class="search-input"
            />
              <button @click="searchLocation" class="search-btn" :disabled="!searchQuery.trim() || isSearching">
                {{ isSearching ? '搜索中...' : '搜索' }}
              </button>
            </div>
            <div v-if="searchResults.length > 0" class="search-results">
              <div 
                v-for="(result, index) in searchResults" 
                :key="index"
                @click="selectLocation(result)"
                class="search-result-item"
              >
                <div class="result-name">{{ result.place_name }}</div>
                <div class="result-type">{{ result.place_type?.join(', ') || '地点' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 参数设置 -->
        <div class="section">
          <h3>地图设置</h3>
          
          <!-- 卫星地图源设置 -->
        <div class="form-group">
            <label>卫星地图源 URL:</label>
            <div class="map-url-input-group">
              <input 
                v-model="customMapUrlInput" 
                type="text" 
                placeholder="例如: http://example.com/{z}/{x}/{y}.png"
                class="map-url-input"
                @keyup.enter="applyCustomMapUrl"
              />
              <button @click="applyCustomMapUrl" class="apply-btn" :disabled="!customMapUrlInput">
                应用
              </button>
              <button @click="clearCustomMapUrl" class="clear-map-btn" v-if="customMapUrl">
                清除
              </button>
            </div>
            <small class="hint">💡 支持 {z}/{x}/{y} 瓦片格式，按回车或点击应用按钮生效</small>
          </div>
          
          <!-- 当前使用的地图源信息 -->
          <div class="form-group" v-if="customMapUrl">
            <label>当前地图源:</label>
            <div class="map-source-info">
              <small>{{ customMapUrl }}</small>
            </div>
          </div>
          
          <div class="form-group">
            <label>中心点经度:</label>
            <input v-model.number="mapCenter.lng" type="number" step="0.000001" @change="updateMapCenter" />
          </div>
          <div class="form-group">
            <label>中心点纬度:</label>
            <input v-model.number="mapCenter.lat" type="number" step="0.000001" @change="updateMapCenter" />
          </div>
                      <div class="form-group">
              <label>缩放级别:</label>
              <input v-model.number="mapZoom" type="number" min="1" max="20" step="0.01" @change="updateMapZoom" />
            </div>
          <div class="form-group">
            <label>
              <input v-model="useSatelliteMap" type="checkbox" @change="toggleMapStyle" />
              卫星地图
            </label>
          </div>
          <div class="form-group">
            <div class="center-point-actions">
              <button @click="saveCurrentCenter" class="save-center-btn">
                保存当前中心点
              </button>
              <small class="hint">💡 点击地图任意位置可快速设置中心点</small>
            </div>
          </div>
        </div>

        <!-- 保存的中心点 -->
        <div class="section" v-if="savedCenters.length > 0">
          <h3>保存的中心点</h3>
          <div class="saved-centers">
            <div 
              v-for="(center, index) in savedCenters" 
              :key="index"
              class="saved-center-item"
            >
              <div class="center-info">
                <div class="center-name">{{ center.name }}</div>
                <div class="center-coords">
                  {{ center.lng.toFixed(6) }}, {{ center.lat.toFixed(6) }}
              </div>
                <div class="center-meta">缩放: {{ center.zoom }} | {{ center.date }}</div>
              </div>
              <div class="center-actions">
                <button @click="jumpToCenter(center)" class="jump-btn" title="跳转到此位置">
                  📍
                </button>
                <button @click="deleteSavedCenter(index)" class="delete-btn" title="删除">
                  🗑️
                </button>
            </div>
          </div>
        </div>
      </div>

        <!-- SVG 大小和旋转调整 -->
        <div class="section" v-if="showingSvg">
          <h3>SVG 调整</h3>
          <div class="form-group" v-if="svgAspectRatio !== 1">
            <label>高宽比: {{ svgAspectRatio.toFixed(2) }}</label>
            <small class="hint">{{ svgAspectRatio >= 1 ? '横向' : '纵向' }}图形，调整时会保持高宽比</small>
          </div>
        <div class="form-group">
            <label>大小: {{ svgSize }}像素</label>
            <input 
              v-model.number="svgSize" 
              type="range" 
              min="50" 
              max="800" 
              step="10" 
              @input="updateSvgSize"
              class="slider"
            />
            <input v-model.number="svgSize" type="number" step="10" min="50" max="1000" @change="updateSvgSize" />
          </div>
          <div class="form-group">
            <label>旋转角度: {{ svgRotation }}°</label>
            <input 
              v-model.number="svgRotation" 
              type="range" 
              min="-360" 
              max="360" 
              step="1" 
              @input="updateSvgRotation"
              class="slider"
            />
            <input v-model.number="svgRotation" type="number" min="-360" max="360" @change="updateSvgRotation" />
        </div>

        
          <!-- 显示记录的GeoJSON中心点 -->
          <div class="form-group" v-if="geoJsonCenter">
            <label>GeoJSON中心点:</label>
            <div class="center-display">
              <small>经度: {{ geoJsonCenter.lng.toFixed(6) }}</small>
              <small>纬度: {{ geoJsonCenter.lat.toFixed(6) }}</small>
              <small class="hint">💡 拖动GeoJSON可更新中心点</small>
            </div>
        </div>
      </div>

        <!-- GeoJSON 样式控制 -->
      <div class="section" v-if="geoJsonData">
          <div class="section-header">
        <h3>GeoJSON 样式</h3>
            <button @click="clearGeoJSON" class="clear-geojson-btn" title="清空GeoJSON">
              🗑️
            </button>
          </div>
        <div class="form-group">
          <label>线条颜色:</label>
            <div class="color-input-group">
              <input 
                v-model="geoJsonStrokeColor" 
                type="color" 
                @change="updateGeoJSONColors"
                class="color-picker"
              />
              <input 
                v-model="geoJsonStrokeColor" 
                type="text" 
                @change="updateGeoJSONColors"
                class="color-text"
                placeholder="#ff0000"
              />
            </div>
        </div>
        <div class="form-group">
          <label>填充颜色:</label>
            <div class="color-input-group">
              <input 
                v-model="geoJsonFillColor" 
                type="color" 
                @change="updateGeoJSONColors"
                class="color-picker"
              />
              <input 
                v-model="geoJsonFillColor" 
                type="text" 
                @change="updateGeoJSONColors"
                class="color-text"
                placeholder="#0000ff"
              />
            </div>
        </div>
        <div class="form-group">
            <label>线条宽度: {{ geoJsonStrokeWidth }}px</label>
            <input 
              v-model.number="geoJsonStrokeWidth" 
              type="range" 
              min="1" 
              max="10" 
              step="1" 
              @input="updateGeoJSONColors"
              class="slider"
            />
            <input 
              v-model.number="geoJsonStrokeWidth" 
              type="number" 
              min="1" 
              max="20" 
              @change="updateGeoJSONColors"
            />
        </div>
      </div>

      <!-- 显示控制 -->
        <div class="section" v-if="svgContent || geoJsonData">
        <h3>显示控制</h3>
          <div class="form-group" v-if="svgContent">
          <label>
            <input v-model="showSVG" type="checkbox" @change="toggleSVGDisplay" />
            显示 SVG
          </label>
        </div>
          <div class="form-group" v-if="geoJsonData">
          <label>
            <input v-model="showGeoJSON" type="checkbox" @change="toggleGeoJSONDisplay" />
            显示 GeoJSON
          </label>
        </div>
      </div>

      <!-- 输出控制 -->
        <div class="section" v-if="svgContent || geoJsonData">
        <h3>输出控制</h3>
          
          <!-- SVG转换控制 -->
          <div v-if="showingSvg" class="form-group">
            <div class="svg-convert-instructions">
              <small class="hint">
                💡 调整好SVG图片的位置和大小后，点击下方按钮转换为GeoJSON
              </small>
            </div>
            <button @click="convertToGeoJSON" class="convert-btn" :disabled="isConverting">
              {{ convertButtonText }}
            </button>
          </div>
          
          <!-- 重新调整SVG -->
          <div v-if="geoJsonData && !showingSvg && svgContent" class="form-group">
            <button @click="resetToSvgMode" class="reset-svg-btn">
              🔄 重新调整SVG
            </button>
          </div>

          <!-- GeoJSON相关操作 -->
          <div v-if="geoJsonData" class="action-group">
          <div class="button-row">
            <button @click="downloadGeoJSON" class="download-btn">
              💾 下载 GeoJSON
            </button>
            <button @click="copyGeoJSONToClipboard" class="copy-btn">
              📋 复制到剪贴板
            </button>
          </div>
          <div class="button-row" v-if="updatedSvgContent">
            <button @click="downloadUpdatedSVG" class="download-btn">
              📄 下载包含MetaInfo的SVG
            </button>
          </div>
            <button @click="submitGeoJSON" class="submit-btn">
              📤 提交 GeoJSON
            </button>
          <button @click="fitMapToGeoJSON" class="fit-btn">
            🎯 适应地图视图
          </button>
            <button @click="resetToSvgMode" class="reset-position-btn">
              🔄 重置位置
            </button>
          </div>
        </div>
        </div>
      </div>

      <!-- 状态信息 -->
    <div v-if="statusMessage" class="status-message" :class="statusType">
        {{ statusMessage }}
    </div>
  </div>
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}

/* SVG转换控制样式 */
.svg-convert-instructions {
  margin-bottom: 10px;
}

.convert-btn {
  width: 100%;
  padding: 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.convert-btn:hover:not(:disabled) {
  background: #0056b3;
}

.convert-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.reset-svg-btn {
  width: 100%;
  padding: 10px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reset-svg-btn:hover {
  background: #1e7e34;
}

.hint {
  display: block;
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  line-height: 1.4;
}

.center-display {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  margin-top: 4px;
}

.center-display small {
  display: block;
  margin: 2px 0;
  font-family: monospace;
}

.center-display .hint {
  color: #666;
  font-style: italic;
  margin-top: 4px;
}
</style>
