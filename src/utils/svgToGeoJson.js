/**
 * SVG 转 GeoJSON 工具函数 - 简化版本
 * 基于SVG图片在地图上的边界坐标进行转换
 */

/**
 * 基于SVG边界坐标转换SVG为GeoJSON
 * @param {string} svgString - SVG字符串
 * @param {Object} bounds - SVG在地图上的边界坐标
 * @param {Object} bounds.topLeft - 左上角坐标 {lng, lat}
 * @param {Object} bounds.bottomRight - 右下角坐标 {lng, lat}
 * @param {number} rotation - 旋转角度（度，顺时针为正）
 * @returns {Object} GeoJSON对象
 */
export function svgToGeoJsonByBounds(svgString, bounds, rotation = 0) {
  if (!bounds || !bounds.topLeft || !bounds.bottomRight) {
    throw new Error("必须提供SVG在地图上的边界坐标");
  }

  console.log("开始转换SVG，边界坐标:", bounds);
  console.log("SVG旋转角度:", rotation);

  // 解析SVG
  const parser = new DOMParser();
  const svgDoc = parser.parseFromString(svgString, "image/svg+xml");
  const svgElement = svgDoc.querySelector("svg");

  if (!svgElement) {
    throw new Error("无效的SVG格式");
  }

  // 获取SVG尺寸
  const viewBox = svgElement.getAttribute("viewBox");
  let svgWidth,
    svgHeight,
    svgMinX = 0,
    svgMinY = 0;

  if (viewBox) {
    const [minX, minY, width, height] = viewBox.split(/\s+/).map(Number);
    svgMinX = minX;
    svgMinY = minY;
    svgWidth = width;
    svgHeight = height;
    console.log("SVG使用viewBox:", { minX, minY, width, height });
  } else {
    svgWidth = parseFloat(svgElement.getAttribute("width")) || 100;
    svgHeight = parseFloat(svgElement.getAttribute("height")) || 100;
    console.log("SVG使用width/height属性:", { svgWidth, svgHeight });
  }

  console.log("SVG尺寸:", { svgWidth, svgHeight, svgMinX, svgMinY });

  // 优先查找g标签内的内容
  let targetContainer = svgElement;
  const gElements = svgElement.querySelectorAll("g");

  // 如果有g标签，优先使用第一个包含图形元素的g标签
  if (gElements.length > 0) {
    for (const g of gElements) {
      const graphicElements = g.querySelectorAll(
        "path, polygon, polyline, rect, circle, ellipse, line"
      );
      if (graphicElements.length > 0) {
        targetContainer = g;
        console.log(
          `svgToGeoJson使用g标签作为内容容器: id="${
            g.getAttribute("id") || "未命名"
          }", 包含${graphicElements.length}个图形元素`
        );
        break;
      }
    }
  }

  // 获取所有SVG元素以计算实际内容边界
  const elements = targetContainer.querySelectorAll(
    "path, polygon, polyline, rect, circle, ellipse, line"
  );

  // 计算SVG内容的实际边界
  let contentMinX = Infinity;
  let contentMinY = Infinity;
  let contentMaxX = -Infinity;
  let contentMaxY = -Infinity;

  console.log(`svgToGeoJson分析${elements.length}个图形元素的边界`);
  elements.forEach((element) => {
    const bbox = getElementBounds(element);
    if (bbox) {
      console.log(`svgToGeoJson元素${element.tagName}边界:`, bbox);
      contentMinX = Math.min(contentMinX, bbox.minX);
      contentMinY = Math.min(contentMinY, bbox.minY);
      contentMaxX = Math.max(contentMaxX, bbox.maxX);
      contentMaxY = Math.max(contentMaxY, bbox.maxY);
    }
  });

  // 如果没有找到内容边界，使用整个SVG区域
  if (!isFinite(contentMinX)) {
    contentMinX = svgMinX;
    contentMinY = svgMinY;
    contentMaxX = svgMinX + svgWidth;
    contentMaxY = svgMinY + svgHeight;
  }

  const contentWidth = contentMaxX - contentMinX;
  const contentHeight = contentMaxY - contentMinY;

  console.log("SVG内容实际边界:", {
    contentMinX,
    contentMinY,
    contentMaxX,
    contentMaxY,
    contentWidth,
    contentHeight,
  });

  // 分析内容边界与SVG画布的关系
  console.log("边界分析:", {
    内容是否超出画布: {
      X轴: contentMinX < svgMinX || contentMaxX > svgMinX + svgWidth,
      Y轴: contentMinY < svgMinY || contentMaxY > svgMinY + svgHeight,
    },
    SVG画布范围: {
      X: `${svgMinX} - ${svgMinX + svgWidth}`,
      Y: `${svgMinY} - ${svgMinY + svgHeight}`,
    },
    内容实际范围: {
      X: `${contentMinX} - ${contentMaxX}`,
      Y: `${contentMinY} - ${contentMaxY}`,
    },
  });

  // 检测内容是否超出原始画布边界
  const contentExceedsCanvas =
    contentMinX < svgMinX ||
    contentMinY < svgMinY ||
    contentMaxX > svgMinX + svgWidth ||
    contentMaxY > svgMinY + svgHeight;

  console.log("svgToGeoJson边界检测:", {
    原始画布: { svgMinX, svgMinY, width: svgWidth, height: svgHeight },
    内容边界: { contentMinX, contentMinY, contentMaxX, contentMaxY },
    内容超出画布: contentExceedsCanvas,
  });

  // 保存原始画布尺寸（关键修复）
  const originalSvgWidth = svgWidth;
  const originalSvgHeight = svgHeight;

  // 如果内容超出，扩展画布以包含所有内容
  if (contentExceedsCanvas) {
    const newMinX = Math.min(svgMinX, contentMinX);
    const newMinY = Math.min(svgMinY, contentMinY);
    const newMaxX = Math.max(svgMinX + svgWidth, contentMaxX);
    const newMaxY = Math.max(svgMinY + svgHeight, contentMaxY);

    svgMinX = newMinX;
    svgMinY = newMinY;
    svgWidth = newMaxX - newMinX;
    svgHeight = newMaxY - newMinY;

    console.log("扩展画布:", {
      扩展前: { width: originalSvgWidth, height: originalSvgHeight },
      扩展后: { svgMinX, svgMinY, width: svgWidth, height: svgHeight },
    });
  }

  // 计算地理坐标范围
  const lngRange = bounds.bottomRight.lng - bounds.topLeft.lng;
  const latRange = bounds.topLeft.lat - bounds.bottomRight.lat;

  // 计算缩放比例（基于内容尺寸，因为bounds现在对应内容）
  const lngScale = lngRange / contentWidth;
  const latScale = latRange / contentHeight;

  console.log("转换比例:", { lngScale, latScale });
  console.log("传入的bounds现在对应内容边界，使用原始画布尺寸作为坐标基准");

  // 计算内容中心点的地理坐标
  const contentCenterX = contentMinX + contentWidth / 2;
  const contentCenterY = contentMinY + contentHeight / 2;

  /**
   * 应用旋转变换
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} centerX - 旋转中心X坐标
   * @param {number} centerY - 旋转中心Y坐标
   * @param {number} rotationDegrees - 旋转角度（度，顺时针为正）
   * @returns {Object} {x, y} 旋转后的坐标
   */
  function applyRotation(x, y, centerX, centerY, rotationDegrees) {
    if (rotationDegrees === 0) {
      return { x, y };
    }

    // 取反旋转角度，确保与CSS transform方向一致
    const radians = (-rotationDegrees * Math.PI) / 180;
    const cos = Math.cos(radians);
    const sin = Math.sin(radians);

    // 平移到原点
    const translatedX = x - centerX;
    const translatedY = y - centerY;

    // 应用旋转
    const rotatedX = translatedX * cos - translatedY * sin;
    const rotatedY = translatedX * sin + translatedY * cos;

    // 平移回原位置
    return {
      x: rotatedX + centerX,
      y: rotatedY + centerY,
    };
  }

  /**
   * SVG坐标转地理坐标（直接映射到内容边界）
   * @param {number} x - SVG X坐标
   * @param {number} y - SVG Y坐标
   * @returns {Array} [lng, lat]
   */
  function svgToGeoCoords(x, y) {
    // 应用旋转变换（绕内容中心旋转）
    const rotated = applyRotation(
      x,
      y,
      contentCenterX,
      contentCenterY,
      rotation
    );

    // 直接映射到传入的地理边界（bounds对应内容边界）
    const relativeX = (rotated.x - contentMinX) / contentWidth;
    const relativeY = (rotated.y - contentMinY) / contentHeight;

    const lng = bounds.topLeft.lng + relativeX * lngRange;
    const lat = bounds.topLeft.lat - relativeY * latRange; // Y轴需要反向

    // 添加调试信息 - 仅对特殊点进行调试
    if (Math.abs(x - contentMinX) < 1 && Math.abs(y - contentMinY) < 1) {
      console.log("转换内容左上角:", {
        原始: { x, y },
        旋转后: rotated,
        相对坐标: { relativeX, relativeY },
        地理坐标: { lng, lat },
      });
    }
    if (Math.abs(x - contentMaxX) < 1 && Math.abs(y - contentMaxY) < 1) {
      console.log("转换内容右下角:", {
        原始: { x, y },
        旋转后: rotated,
        相对坐标: { relativeX, relativeY },
        地理坐标: { lng, lat },
      });
    }
    if (Math.abs(x - contentCenterX) < 1 && Math.abs(y - contentCenterY) < 1) {
      console.log("转换内容中心:", {
        原始: { x, y },
        旋转后: rotated,
        相对坐标: { relativeX, relativeY },
        地理坐标: { lng, lat },
      });
    }

    return [lng, lat];
  }

  const centerGeoCoords = svgToGeoCoords(contentCenterX, contentCenterY);
  console.log("内容中心点:", {
    svgCenter: [contentCenterX, contentCenterY],
    geoCenter: centerGeoCoords,
  });

  // 创建GeoJSON特征集合
  const features = [];

  // 处理所有SVG元素（使用之前已经查询的elements）
  elements.forEach((element, index) => {
    try {
      const feature = convertElementToGeoJSON(element, svgToGeoCoords, index);
      if (feature) {
        features.push(feature);
      }
    } catch (error) {
      console.warn(`转换元素失败:`, element, error);
    }
  });

  const geoJson = {
    type: "FeatureCollection",
    features: features,
    // 添加调试信息
    _debug: {
      svgBounds: { svgMinX, svgMinY, svgWidth, svgHeight },
      contentBounds: {
        contentMinX,
        contentMinY,
        contentMaxX,
        contentMaxY,
        contentWidth,
        contentHeight,
      },
    },
  };

  console.log(`转换完成，生成了 ${features.length} 个特征`);

  return geoJson;
}

/**
 * 将SVG元素转换为GeoJSON特征
 * @param {Element} element - SVG元素
 * @param {Function} coordTransform - 坐标转换函数
 * @param {number} index - 元素索引
 * @returns {Object|null} GeoJSON特征
 */
function convertElementToGeoJSON(element, coordTransform, index) {
  const tagName = element.tagName.toLowerCase();

  switch (tagName) {
    case "path":
      return convertPath(element, coordTransform, index);
    case "polygon":
      return convertPolygon(element, coordTransform, index);
    case "polyline":
      return convertPolyline(element, coordTransform, index);
    case "rect":
      return convertRect(element, coordTransform, index);
    case "circle":
      return convertCircle(element, coordTransform, index);
    case "ellipse":
      return convertEllipse(element, coordTransform, index);
    case "line":
      return convertLine(element, coordTransform, index);
    default:
      console.warn(`不支持的SVG元素类型: ${tagName}`);
      return null;
  }
}

/**
 * 转换path元素
 */
function convertPath(element, coordTransform, index) {
  const pathData = element.getAttribute("d");
  if (!pathData) return null;

  // 分割成多个子路径（以M命令为分界）
  const subPaths = splitPathIntoSubPaths(pathData, coordTransform);

  if (subPaths.length === 0) return null;

  // 如果只有一个子路径，按原逻辑处理
  if (subPaths.length === 1) {
    const coordinates = subPaths[0];
    const isClosed = pathData.toLowerCase().includes("z");

    if (isClosed && coordinates.length >= 3) {
      // 确保多边形闭合
      const firstPoint = coordinates[0];
      const lastPoint = coordinates[coordinates.length - 1];
      if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
        coordinates.push([firstPoint[0], firstPoint[1]]);
      }

      return {
        type: "Feature",
        properties: {
          id: `path_${index}`,
          type: "path",
          originalElement: "path",
        },
        geometry: {
          type: "Polygon",
          coordinates: [coordinates],
        },
      };
    } else {
      return {
        type: "Feature",
        properties: {
          id: `path_${index}`,
          type: "path",
          originalElement: "path",
        },
        geometry: {
          type: "LineString",
          coordinates: coordinates,
        },
      };
    }
  }

  // 多个子路径，创建MultiPolygon或MultiLineString
  const isClosed = pathData.toLowerCase().includes("z");

  if (isClosed) {
    // 多个封闭路径，创建MultiPolygon
    const polygonCoordinates = subPaths.map((subPath) => {
      // 确保每个子路径闭合
      const firstPoint = subPath[0];
      const lastPoint = subPath[subPath.length - 1];
      if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
        subPath.push([firstPoint[0], firstPoint[1]]);
      }
      return [subPath]; // 每个多边形是一个坐标数组的数组
    });

    return {
      type: "Feature",
      properties: {
        id: `path_${index}`,
        type: "path",
        originalElement: "path",
      },
      geometry: {
        type: "MultiPolygon",
        coordinates: polygonCoordinates,
      },
    };
  } else {
    // 多个开放路径，创建MultiLineString
    return {
      type: "Feature",
      properties: {
        id: `path_${index}`,
        type: "path",
        originalElement: "path",
      },
      geometry: {
        type: "MultiLineString",
        coordinates: subPaths,
      },
    };
  }
}

/**
 * 将复杂路径分割成多个子路径
 */
function splitPathIntoSubPaths(pathData, coordTransform) {
  const subPaths = [];

  // 按M命令分割路径
  const movetoPattern = /M[^Mm]*/gi;
  const pathSegments = pathData.match(movetoPattern);

  if (!pathSegments) {
    // 如果没有M命令，尝试解析整个路径
    const coordinates = parseSvgPath(pathData, coordTransform);
    if (coordinates.length > 0) {
      subPaths.push(coordinates);
    }
    return subPaths;
  }

  pathSegments.forEach((segment) => {
    const coordinates = parseSvgPath(segment, coordTransform);
    if (coordinates.length > 0) {
      subPaths.push(coordinates);
    }
  });

  return subPaths;
}

/**
 * 转换polygon元素
 */
function convertPolygon(element, coordTransform, index) {
  const points = element.getAttribute("points");
  if (!points) return null;

  const coordinates = parsePointsString(points, coordTransform);
  if (coordinates.length < 3) return null;

  // 确保多边形闭合
  const firstPoint = coordinates[0];
  const lastPoint = coordinates[coordinates.length - 1];
  if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
    coordinates.push([firstPoint[0], firstPoint[1]]);
  }

  return {
    type: "Feature",
    properties: {
      id: `polygon_${index}`,
      type: "polygon",
      originalElement: "polygon",
    },
    geometry: {
      type: "Polygon",
      coordinates: [coordinates],
    },
  };
}

/**
 * 转换polyline元素
 */
function convertPolyline(element, coordTransform, index) {
  const points = element.getAttribute("points");
  if (!points) return null;

  const coordinates = parsePointsString(points, coordTransform);
  if (coordinates.length < 2) return null;

  return {
    type: "Feature",
    properties: {
      id: `polyline_${index}`,
      type: "polyline",
      originalElement: "polyline",
    },
    geometry: {
      type: "LineString",
      coordinates: coordinates,
    },
  };
}

/**
 * 转换rect元素
 */
function convertRect(element, coordTransform, index) {
  const x = parseFloat(element.getAttribute("x") || 0);
  const y = parseFloat(element.getAttribute("y") || 0);
  const width = parseFloat(element.getAttribute("width") || 0);
  const height = parseFloat(element.getAttribute("height") || 0);

  if (width <= 0 || height <= 0) return null;

  const coordinates = [
    coordTransform(x, y),
    coordTransform(x + width, y),
    coordTransform(x + width, y + height),
    coordTransform(x, y + height),
    coordTransform(x, y), // 闭合
  ];

  return {
    type: "Feature",
    properties: {
      id: `rect_${index}`,
      type: "rect",
      originalElement: "rect",
    },
    geometry: {
      type: "Polygon",
      coordinates: [coordinates],
    },
  };
}

/**
 * 转换circle元素（近似为多边形）
 */
function convertCircle(element, coordTransform, index) {
  const cx = parseFloat(element.getAttribute("cx") || 0);
  const cy = parseFloat(element.getAttribute("cy") || 0);
  const r = parseFloat(element.getAttribute("r") || 0);

  if (r <= 0) return null;

  const segments = 24; // 圆形近似的边数
  const coordinates = [];

  for (let i = 0; i <= segments; i++) {
    const angle = (i * 2 * Math.PI) / segments;
    const x = cx + r * Math.cos(angle);
    const y = cy + r * Math.sin(angle);
    coordinates.push(coordTransform(x, y));
  }

  return {
    type: "Feature",
    properties: {
      id: `circle_${index}`,
      type: "circle",
      originalElement: "circle",
      radius: r,
    },
    geometry: {
      type: "Polygon",
      coordinates: [coordinates],
    },
  };
}

/**
 * 转换ellipse元素（近似为多边形）
 */
function convertEllipse(element, coordTransform, index) {
  const cx = parseFloat(element.getAttribute("cx") || 0);
  const cy = parseFloat(element.getAttribute("cy") || 0);
  const rx = parseFloat(element.getAttribute("rx") || 0);
  const ry = parseFloat(element.getAttribute("ry") || 0);

  if (rx <= 0 || ry <= 0) return null;

  const segments = 24;
  const coordinates = [];

  for (let i = 0; i <= segments; i++) {
    const angle = (i * 2 * Math.PI) / segments;
    const x = cx + rx * Math.cos(angle);
    const y = cy + ry * Math.sin(angle);
    coordinates.push(coordTransform(x, y));
  }

  return {
    type: "Feature",
    properties: {
      id: `ellipse_${index}`,
      type: "ellipse",
      originalElement: "ellipse",
      radiusX: rx,
      radiusY: ry,
    },
    geometry: {
      type: "Polygon",
      coordinates: [coordinates],
    },
  };
}

/**
 * 转换line元素
 */
function convertLine(element, coordTransform, index) {
  const x1 = parseFloat(element.getAttribute("x1") || 0);
  const y1 = parseFloat(element.getAttribute("y1") || 0);
  const x2 = parseFloat(element.getAttribute("x2") || 0);
  const y2 = parseFloat(element.getAttribute("y2") || 0);

  const coordinates = [coordTransform(x1, y1), coordTransform(x2, y2)];

  return {
    type: "Feature",
    properties: {
      id: `line_${index}`,
      type: "line",
      originalElement: "line",
    },
    geometry: {
      type: "LineString",
      coordinates: coordinates,
    },
  };
}

/**
 * 解析SVG路径数据（改进版本，更精确地处理路径命令）
 */
function parseSvgPath(pathData, coordTransform) {
  const coordinates = [];

  // 更精确的命令分割，保持命令和其参数的关联
  const commandPattern =
    /([MLHVCSQTAZmlhvcsqtaz])\s*([^MLHVCSQTAZmlhvcsqtaz]*)/g;
  const commands = [];
  let match;

  while ((match = commandPattern.exec(pathData)) !== null) {
    commands.push({
      type: match[1],
      args: match[2]
        .trim()
        .split(/[\s,]+/)
        .filter((s) => s && !isNaN(s))
        .map(Number),
    });
  }

  let currentX = 0;
  let currentY = 0;
  let pathStartX = 0;
  let pathStartY = 0;

  commands.forEach((command) => {
    const { type, args } = command;

    switch (type.toLowerCase()) {
      case "m": // moveto
        if (args.length >= 2) {
          if (type === "m") {
            // 相对移动
            currentX += args[0];
            currentY += args[1];
          } else {
            // 绝对移动
            currentX = args[0];
            currentY = args[1];
          }

          // 记录路径起始点（用于Z命令）
          pathStartX = currentX;
          pathStartY = currentY;

          coordinates.push(coordTransform(currentX, currentY));

          // M命令后的其余参数被视为L命令
          for (let i = 2; i < args.length; i += 2) {
            if (i + 1 < args.length) {
              if (type === "m") {
                currentX += args[i];
                currentY += args[i + 1];
              } else {
                currentX = args[i];
                currentY = args[i + 1];
              }
              coordinates.push(coordTransform(currentX, currentY));
            }
          }
        }
        break;

      case "l": // lineto
        for (let i = 0; i < args.length; i += 2) {
          if (i + 1 < args.length) {
            if (type === "l") {
              // 相对
              currentX += args[i];
              currentY += args[i + 1];
            } else {
              // 绝对
              currentX = args[i];
              currentY = args[i + 1];
            }
            coordinates.push(coordTransform(currentX, currentY));
          }
        }
        break;

      case "h": // 水平线
        for (let i = 0; i < args.length; i++) {
          if (type === "h") {
            // 相对
            currentX += args[i];
          } else {
            // 绝对
            currentX = args[i];
          }
          coordinates.push(coordTransform(currentX, currentY));
        }
        break;

      case "v": // 垂直线
        for (let i = 0; i < args.length; i++) {
          if (type === "v") {
            // 相对
            currentY += args[i];
          } else {
            // 绝对
            currentY = args[i];
          }
          coordinates.push(coordTransform(currentX, currentY));
        }
        break;

      case "c": // 三次贝塞尔曲线（简化为直线）
        for (let i = 0; i < args.length; i += 6) {
          if (i + 5 < args.length) {
            if (type === "c") {
              // 相对
              currentX += args[i + 4];
              currentY += args[i + 5];
            } else {
              // 绝对
              currentX = args[i + 4];
              currentY = args[i + 5];
            }
            coordinates.push(coordTransform(currentX, currentY));
          }
        }
        break;

      case "s": // 平滑三次贝塞尔曲线（简化为直线）
        for (let i = 0; i < args.length; i += 4) {
          if (i + 3 < args.length) {
            if (type === "s") {
              // 相对
              currentX += args[i + 2];
              currentY += args[i + 3];
            } else {
              // 绝对
              currentX = args[i + 2];
              currentY = args[i + 3];
            }
            coordinates.push(coordTransform(currentX, currentY));
          }
        }
        break;

      case "q": // 二次贝塞尔曲线（简化为直线）
        for (let i = 0; i < args.length; i += 4) {
          if (i + 3 < args.length) {
            if (type === "q") {
              // 相对
              currentX += args[i + 2];
              currentY += args[i + 3];
            } else {
              // 绝对
              currentX = args[i + 2];
              currentY = args[i + 3];
            }
            coordinates.push(coordTransform(currentX, currentY));
          }
        }
        break;

      case "t": // 平滑二次贝塞尔曲线（简化为直线）
        for (let i = 0; i < args.length; i += 2) {
          if (i + 1 < args.length) {
            if (type === "t") {
              // 相对
              currentX += args[i];
              currentY += args[i + 1];
            } else {
              // 绝对
              currentX = args[i];
              currentY = args[i + 1];
            }
            coordinates.push(coordTransform(currentX, currentY));
          }
        }
        break;

      case "a": // 椭圆弧（简化处理，只取终点）
        for (let i = 0; i < args.length; i += 7) {
          if (i + 6 < args.length) {
            if (type === "a") {
              // 相对
              currentX += args[i + 5];
              currentY += args[i + 6];
            } else {
              // 绝对
              currentX = args[i + 5];
              currentY = args[i + 6];
            }
            coordinates.push(coordTransform(currentX, currentY));
          }
        }
        break;

      case "z": // 闭合路径
        // 回到路径起始点
        if (pathStartX !== currentX || pathStartY !== currentY) {
          coordinates.push(coordTransform(pathStartX, pathStartY));
          currentX = pathStartX;
          currentY = pathStartY;
        }
        break;
    }
  });

  return coordinates;
}

/**
 * 解析points字符串
 */
function parsePointsString(pointsString, coordTransform) {
  const points = pointsString
    .trim()
    .split(/[\s,]+/)
    .filter((s) => s);
  const coordinates = [];

  for (let i = 0; i < points.length; i += 2) {
    if (i + 1 < points.length) {
      const x = parseFloat(points[i]);
      const y = parseFloat(points[i + 1]);
      if (!isNaN(x) && !isNaN(y)) {
        coordinates.push(coordTransform(x, y));
      }
    }
  }

  return coordinates;
}

/**
 * 清理GeoJSON坐标，移除无效值
 */
export function cleanGeoJsonCoordinates(geoJson) {
  if (!geoJson || !geoJson.features) return geoJson;

  const cleanedFeatures = geoJson.features
    .map((feature) => {
      if (!feature.geometry || !feature.geometry.coordinates) return feature;

      const cleanedFeature = { ...feature };
      const geometry = feature.geometry;

      if (geometry.type === "Point") {
        const [lng, lat] = geometry.coordinates;
        if (isValidCoordinate(lng, lat)) {
          cleanedFeature.geometry.coordinates = [lng, lat];
        } else {
          return null; // 无效的点
        }
      } else if (geometry.type === "LineString") {
        const validCoords = geometry.coordinates.filter((coord) => {
          const [lng, lat] = coord;
          return isValidCoordinate(lng, lat);
        });
        if (validCoords.length >= 2) {
          cleanedFeature.geometry.coordinates = validCoords;
        } else {
          return null; // 线段点数不足
        }
      } else if (geometry.type === "Polygon") {
        const validRings = geometry.coordinates
          .map((ring) => {
            return ring.filter((coord) => {
              const [lng, lat] = coord;
              return isValidCoordinate(lng, lat);
            });
          })
          .filter((ring) => ring.length >= 4); // 多边形至少需要4个点（闭合）

        if (validRings.length > 0) {
          cleanedFeature.geometry.coordinates = validRings;
        } else {
          return null; // 无有效的环
        }
      }

      return cleanedFeature;
    })
    .filter((feature) => feature !== null);

  return {
    ...geoJson,
    features: cleanedFeatures,
  };
}

/**
 * 检查坐标是否有效
 * @param {number} lng - 经度
 * @param {number} lat - 纬度
 * @returns {boolean} 是否有效
 */
function isValidCoordinate(lng, lat) {
  return (
    typeof lng === "number" &&
    typeof lat === "number" &&
    isFinite(lng) &&
    isFinite(lat) &&
    lng >= -180 &&
    lng <= 180 &&
    lat >= -90 &&
    lat <= 90
  );
}

/**
 * 获取SVG元素的边界框
 * @param {Element} element - SVG元素
 * @returns {Object|null} 边界框 {minX, minY, maxX, maxY}
 */
function getElementBounds(element) {
  const tagName = element.tagName.toLowerCase();

  try {
    switch (tagName) {
      case "rect":
        const x = parseFloat(element.getAttribute("x") || 0);
        const y = parseFloat(element.getAttribute("y") || 0);
        const width = parseFloat(element.getAttribute("width") || 0);
        const height = parseFloat(element.getAttribute("height") || 0);
        return {
          minX: x,
          minY: y,
          maxX: x + width,
          maxY: y + height,
        };

      case "circle":
        const cx = parseFloat(element.getAttribute("cx") || 0);
        const cy = parseFloat(element.getAttribute("cy") || 0);
        const r = parseFloat(element.getAttribute("r") || 0);
        return {
          minX: cx - r,
          minY: cy - r,
          maxX: cx + r,
          maxY: cy + r,
        };

      case "ellipse":
        const ecx = parseFloat(element.getAttribute("cx") || 0);
        const ecy = parseFloat(element.getAttribute("cy") || 0);
        const rx = parseFloat(element.getAttribute("rx") || 0);
        const ry = parseFloat(element.getAttribute("ry") || 0);
        return {
          minX: ecx - rx,
          minY: ecy - ry,
          maxX: ecx + rx,
          maxY: ecy + ry,
        };

      case "line":
        const x1 = parseFloat(element.getAttribute("x1") || 0);
        const y1 = parseFloat(element.getAttribute("y1") || 0);
        const x2 = parseFloat(element.getAttribute("x2") || 0);
        const y2 = parseFloat(element.getAttribute("y2") || 0);
        return {
          minX: Math.min(x1, x2),
          minY: Math.min(y1, y2),
          maxX: Math.max(x1, x2),
          maxY: Math.max(y1, y2),
        };

      case "polygon":
      case "polyline":
        const points = element.getAttribute("points");
        if (!points) return null;
        return getPointsBounds(points);

      case "path":
        const pathData = element.getAttribute("d");
        if (!pathData) return null;
        return getPathBounds(pathData);

      default:
        return null;
    }
  } catch (error) {
    console.warn(`计算元素边界失败:`, element, error);
    return null;
  }
}

/**
 * 获取points字符串的边界框
 * @param {string} pointsString - points属性值
 * @returns {Object|null} 边界框
 */
function getPointsBounds(pointsString) {
  const points = pointsString
    .trim()
    .split(/[\s,]+/)
    .filter((p) => p);
  if (points.length < 4) return null;

  let minX = Infinity,
    minY = Infinity;
  let maxX = -Infinity,
    maxY = -Infinity;

  for (let i = 0; i < points.length; i += 2) {
    const x = parseFloat(points[i]);
    const y = parseFloat(points[i + 1]);
    if (isFinite(x) && isFinite(y)) {
      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x);
      maxY = Math.max(maxY, y);
    }
  }

  return isFinite(minX) ? { minX, minY, maxX, maxY } : null;
}

/**
 * 获取path数据的边界框（简化版本）
 * @param {string} pathData - path的d属性值
 * @returns {Object|null} 边界框
 */
function getPathBounds(pathData) {
  // 简化的路径边界计算，提取所有数字坐标
  const coords = pathData.match(/-?\d+\.?\d*/g);
  if (!coords || coords.length < 4) return null;

  let minX = Infinity,
    minY = Infinity;
  let maxX = -Infinity,
    maxY = -Infinity;

  for (let i = 0; i < coords.length; i += 2) {
    const x = parseFloat(coords[i]);
    const y = parseFloat(coords[i + 1]);
    if (isFinite(x) && isFinite(y)) {
      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x);
      maxY = Math.max(maxY, y);
    }
  }

  return isFinite(minX) ? { minX, minY, maxX, maxY } : null;
}
