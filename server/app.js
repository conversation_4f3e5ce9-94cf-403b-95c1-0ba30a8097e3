const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { geoFromSVGXML } = require('svg2geojson');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 配置multer用于文件上传
const storage = multer.memoryStorage();
const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB限制
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'image/svg+xml' || file.originalname.endsWith('.svg')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传SVG文件'), false);
    }
  }
});

// 创建临时目录
const tempDir = path.join(__dirname, 'temp');
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'SVG转换服务运行正常',
    timestamp: new Date().toISOString()
  });
});

// SVG转换端点 - 接收文件上传
app.post('/convert-svg-file', upload.single('svg'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        error: '没有上传SVG文件' 
      });
    }

    const svgContent = req.file.buffer.toString('utf8');
    console.log('接收到SVG文件:', req.file.originalname);
    console.log('SVG内容长度:', svgContent.length);

    // 转换SVG为GeoJSON
    const geoJson = await convertSvgToGeoJson(svgContent);
    
    res.json({
      success: true,
      data: geoJson,
      message: '转换成功',
      originalFilename: req.file.originalname
    });

  } catch (error) {
    console.error('SVG转换失败:', error);
    res.status(500).json({ 
      error: '转换失败: ' + error.message 
    });
  }
});

// SVG转换端点 - 接收SVG内容字符串
app.post('/convert-svg-content', async (req, res) => {
  try {
    const { svgContent, filename } = req.body;
    
    if (!svgContent) {
      return res.status(400).json({ 
        error: '没有提供SVG内容' 
      });
    }

    console.log('接收到SVG内容，长度:', svgContent.length);
    console.log('文件名:', filename || '未提供');

    // 转换SVG为GeoJSON
    const geoJson = await convertSvgToGeoJson(svgContent);
    
    res.json({
      success: true,
      data: geoJson,
      message: '转换成功',
      filename: filename || 'converted.svg'
    });

  } catch (error) {
    console.error('SVG转换失败:', error);
    res.status(500).json({ 
      error: '转换失败: ' + error.message 
    });
  }
});

// SVG转换核心函数
function convertSvgToGeoJson(svgContent) {
  return new Promise((resolve, reject) => {
    try {
      // 检查SVG是否包含MetaInfo
      if (!svgContent.includes('MetaInfo') || !svgContent.includes('GeoItem')) {
        reject(new Error('SVG文件必须包含MetaInfo和GeoItem元素。请确保SVG包含地理坐标信息。'));
        return;
      }

      console.log('开始转换SVG...');
      
      // 使用svg2geojson进行转换
      geoFromSVGXML(svgContent, (result) => {
        console.log('svg2geojson回调被调用');
        console.log('转换结果类型:', typeof result);
        
        if (result && result.geo) {
          console.log('转换成功，特征数量:', result.geo.features?.length || 0);
          resolve(result.geo);
        } else if (result && typeof result === 'object' && result.features) {
          // 有时候直接返回GeoJSON对象
          console.log('转换成功（直接返回GeoJSON），特征数量:', result.features.length);
          resolve(result);
        } else {
          console.error('转换结果无效:', result);
          reject(new Error('svg2geojson转换失败：返回的数据格式不正确'));
        }
      }, {
        layers: false, // 不分层，返回单个GeoJSON
        tolerance: 0.5 // 容差设置
      });

    } catch (error) {
      console.error('svg2geojson调用异常:', error);
      reject(new Error(`svg2geojson转换失败: ${error.message}`));
    }
  });
}

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: '文件大小超过限制（最大10MB）' });
    }
    return res.status(400).json({ error: '文件上传错误: ' + error.message });
  }
  
  res.status(500).json({ 
    error: '服务器内部错误: ' + error.message 
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ 
    error: '接口不存在',
    availableEndpoints: [
      'GET /health - 健康检查',
      'POST /convert-svg-file - 上传SVG文件转换',
      'POST /convert-svg-content - 发送SVG内容转换'
    ]
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`SVG转换服务已启动，端口: ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
  console.log(`SVG转换API: http://localhost:${PORT}/convert-svg-content`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});
