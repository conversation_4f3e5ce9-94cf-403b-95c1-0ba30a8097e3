{"name": "svg2geojson-server", "version": "1.0.0", "description": "Node.js服务器用于SVG到GeoJSON的转换", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["svg", "g<PERSON><PERSON><PERSON>", "conversion", "nodejs", "express"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "svg2geojson": "^0.7.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}