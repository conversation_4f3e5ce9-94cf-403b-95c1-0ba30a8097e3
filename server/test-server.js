const fs = require('fs');
const path = require('path');

// 测试SVG内容，包含MetaInfo
const testSvgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
  <MetaInfo xmlns="http://www.prognoz.ru">
    <Geo>
      <GeoItem X="0" Y="0" Latitude="39.9042" Longitude="116.4074"/>
      <GeoItem X="100" Y="100" Latitude="39.9000" Longitude="116.4100"/>
    </Geo>
  </MetaInfo>
  <rect x="10" y="10" width="80" height="80" fill="blue" stroke="red" stroke-width="2"/>
  <circle cx="50" cy="50" r="20" fill="yellow"/>
</svg>`;

async function testServer() {
  const serverUrl = 'http://localhost:3001';
  
  console.log('开始测试SVG转换服务器...\n');
  
  try {
    // 1. 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await fetch(`${serverUrl}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ 健康检查通过:', healthData.message);
    console.log('');
    
    // 2. 测试SVG内容转换
    console.log('2. 测试SVG内容转换...');
    const convertResponse = await fetch(`${serverUrl}/convert-svg-content`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        svgContent: testSvgContent,
        filename: 'test.svg'
      })
    });
    
    if (!convertResponse.ok) {
      const errorData = await convertResponse.json();
      throw new Error(errorData.error || `HTTP ${convertResponse.status}`);
    }
    
    const convertData = await convertResponse.json();
    console.log('✅ SVG转换成功');
    console.log('转换结果:', {
      success: convertData.success,
      message: convertData.message,
      featuresCount: convertData.data?.features?.length || 0,
      filename: convertData.filename
    });
    
    // 保存转换结果到文件
    if (convertData.data) {
      const outputPath = path.join(__dirname, 'test-output.geojson');
      fs.writeFileSync(outputPath, JSON.stringify(convertData.data, null, 2));
      console.log(`📁 转换结果已保存到: ${outputPath}`);
    }
    
    console.log('\n🎉 所有测试通过！服务器运行正常。');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.log('\n请确保：');
    console.log('1. Node.js服务器已启动 (npm start)');
    console.log('2. 服务器运行在 http://localhost:3001');
    console.log('3. 所有依赖已正确安装');
    process.exit(1);
  }
}

// 检查是否在Node.js环境中运行
if (typeof fetch === 'undefined') {
  // Node.js 18+ 内置fetch，如果没有则需要polyfill
  console.log('正在安装fetch polyfill...');
  require('node-fetch').then(fetch => {
    global.fetch = fetch;
    testServer();
  }).catch(() => {
    console.log('请安装node-fetch: npm install node-fetch');
    console.log('或使用Node.js 18+版本');
  });
} else {
  testServer();
}
