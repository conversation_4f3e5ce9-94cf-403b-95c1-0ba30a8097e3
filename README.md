# SVG to GeoJSON Converter

一个基于 Vue.js 和 Mapbox 的 SVG 转 GeoJSON 网页工具，可以将 SVG 文件转换为地理数据格式并在地图上可视化显示。

## 功能特性

- 🗺️ **地图显示**: 使用 Mapbox SDK 的全屏地图
- 📁 **文件上传**: 支持 SVG 文件拖拽上传
- 🔍 **地址搜索**: 智能地址搜索并自动定位
- 📍 **地图交互**: 点击地图任意位置快速设置中心点
- 💾 **中心点管理**: 保存常用位置，一键快速跳转
- ⚙️ **参数调整**:
  - 地图中心点坐标设置
  - 地图缩放级别调整
  - 卫星地图/街道地图切换
  - SVG 缩放比例调整（滑块+输入框）
  - SVG 旋转角度调整（滑块+输入框）
- 👁️ **实时预览**:
  - 原始 SVG 图层显示
  - 转换后 GeoJSON 图层显示
  - 两种图层可独立开关
- 🎨 **GeoJSON 样式控制**:
  - 自定义线条颜色
  - 自定义填充颜色
  - 调整线条宽度
  - 实时颜色预览
- 🔄 **双重转换模式**: 支持服务器转换和本地转换
- 📐 **MetaInfo 自动生成**: 自动为 SVG 添加地理坐标信息
- 💾 **多格式下载**: 支持下载 GeoJSON 和包含 MetaInfo 的 SVG 文件

## 技术架构

### 前端

- **框架**: Vue.js 3
- **地图引擎**: Mapbox GL JS
- **构建工具**: Vite
- **样式**: 纯 CSS（响应式设计）

### 后端 (可选)

- **运行时**: Node.js
- **框架**: Express.js
- **转换库**: svg2geojson
- **文件上传**: Multer

### 系统架构图

```
┌─────────────────┐    HTTP API    ┌─────────────────┐
│   前端 (Vue.js)  │ ──────────────► │ 后端 (Node.js)  │
│                 │                │                 │
│ • 文件上传       │                │ • SVG解析       │
│ • 地图交互       │                │ • svg2geojson   │
│ • 位置调整       │                │ • 格式转换      │
│ • 结果显示       │                │ • 错误处理      │
└─────────────────┘                └─────────────────┘
```

## 安装运行

### 方式一：完整模式 (推荐)

1. **启动后端服务**

```bash
# 进入服务器目录
cd server

# 安装依赖
npm install

# 启动服务器
npm start

# 或者使用开发模式
npm run dev
```

服务器将在 `http://localhost:3001` 启动。

2. **启动前端应用**

```bash
# 在项目根目录
npm install
npm run dev
```

前端应用将在 `http://localhost:5173` 启动。

### 方式二：仅前端模式

1. 安装依赖

```bash
npm install
```

2. 设置 Mapbox Access Token
   在 `src/App.vue` 文件中，将 `YOUR_MAPBOX_ACCESS_TOKEN` 替换为你的实际 Access Token：

```javascript
mapboxgl.accessToken = "YOUR_MAPBOX_ACCESS_TOKEN";
```

> 获取免费 Access Token: https://www.mapbox.com/

3. 启动开发服务器

```bash
npm run dev
```

4. 构建生产版本

```bash
npm run build
```

## 使用方法

1. **设置地图中心点**:

   - 在地址搜索框中输入地点名称，系统会自动搜索并提供候选位置
   - 直接点击地图任意位置快速设置中心点坐标
   - 手动输入目标地理坐标（经度、纬度）

2. **中心点管理**:

   - 点击"保存当前中心点"按钮保存常用位置
   - 为保存的位置输入有意义的名称
   - 点击保存位置列表中的 📍 按钮一键跳转到该位置
   - 点击 🗑️ 按钮删除不需要的保存位置

3. **上传 SVG 文件**:

   - 点击"选择 SVG 文件"按钮上传你的 SVG 文件
   - 或点击"加载测试 SVG"使用示例文件
   - **SVG 会自动转换为 GeoJSON 并显示在地图上**

4. **自定义 GeoJSON 样式**:

   - 使用颜色选择器或输入十六进制颜色值调整线条颜色
   - 调整填充颜色
   - 使用滑块调整线条宽度
   - **所有更改会实时应用到地图上**

5. **调整位置和大小**:

   - 点击地图任意位置设置新的中心点（GeoJSON 会自动重新定位）
   - 使用缩放滑块调整 SVG 的大小
   - 使用旋转滑块调整 SVG 的方向
   - **所有调整会自动重新生成 GeoJSON**

6. **设置地图参数**:

   - 设置合适的地图缩放级别
   - 切换卫星地图/街道地图显示

7. **查看和对比**:

   - 勾选"显示原始 SVG"查看 SVG 图层
   - 勾选"显示 GeoJSON"查看转换结果
   - 可以同时显示两种图层进行对比

8. **下载数据**: 点击"下载 GeoJSON"保存转换后的文件

## 支持的 SVG 元素

- ✅ **路径 (path)**: 支持基本路径命令（M, L, H, V, C, Q, Z 等）
- ✅ **矩形 (rect)**: 转换为多边形
- ✅ **圆形 (circle)**: 转换为多边形（32 边近似）
- ✅ **多边形 (polygon)**: 直接转换
- ✅ **样式属性**: 保留填充色、描边色、线宽等样式

## 项目结构

```
src/
├── App.vue                 # 主应用组件
├── style.css              # 样式文件
├── main.js                # 应用入口
└── utils/
    └── svgToGeoJson.js    # SVG转换工具函数
public/
└── sample.svg             # 示例SVG文件
```

## 注意事项

- SVG 坐标系与地理坐标系的转换基于简化算法，适用于大多数场景
- 复杂的贝塞尔曲线会被简化为直线段
- 建议使用相对简单的 SVG 图形以获得最佳转换效果
- 需要有效的 Mapbox Access Token 才能正常显示地图

## 许可证

MIT License
